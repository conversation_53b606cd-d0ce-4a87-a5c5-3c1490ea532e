/**
 * Performance Baseline Test for MDX Provider
 * 
 * This script establishes performance baselines and identifies
 * optimization thresholds for the MDX Provider.
 */

import { MDX<PERSON>rovider } from '../src/services/content/providers/mdx/provider'
import { MemoryCache } from '../src/services/content/providers/mdx/cache/memory-cache'
import { WorkersCache } from '../src/services/content/providers/mdx/cache/workers-cache'

interface PerformanceMetrics {
  operation: string
  iterations: number
  totalTime: number
  averageTime: number
  minTime: number
  maxTime: number
  throughput: number
  memoryBefore: number
  memoryAfter: number
  memoryDelta: number
}

interface BaselineResults {
  provider: string
  strategy: string
  contentCount: number
  metrics: PerformanceMetrics[]
  cacheStats: any
  recommendations: string[]
}

/**
 * Get memory usage in bytes
 */
function getMemoryUsage(): number {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    return process.memoryUsage().heapUsed
  }
  return 0
}

/**
 * Measure performance of an operation
 */
async function measurePerformance<T>(
  operation: string,
  fn: () => Promise<T> | T,
  iterations: number = 100
): Promise<PerformanceMetrics> {
  const times: number[] = []
  const memoryBefore = getMemoryUsage()
  
  // Warm up
  for (let i = 0; i < 5; i++) {
    await fn()
  }
  
  // Measure
  for (let i = 0; i < iterations; i++) {
    const start = performance.now()
    await fn()
    const end = performance.now()
    times.push(end - start)
  }
  
  const memoryAfter = getMemoryUsage()
  const totalTime = times.reduce((sum, time) => sum + time, 0)
  const averageTime = totalTime / iterations
  const minTime = Math.min(...times)
  const maxTime = Math.max(...times)
  const throughput = 1000 / averageTime // ops per second
  
  return {
    operation,
    iterations,
    totalTime,
    averageTime,
    minTime,
    maxTime,
    throughput,
    memoryBefore,
    memoryAfter,
    memoryDelta: memoryAfter - memoryBefore
  }
}

/**
 * Test MDX Provider performance
 */
async function testMDXProviderPerformance(
  providerName: string,
  forceStrategy?: 'precompiled' | 'filesystem'
): Promise<BaselineResults> {
  console.log(`\n🚀 Testing ${providerName} Performance...`)
  
  const provider = new MDXProvider(forceStrategy ? { forceStrategy } : {})
  const metrics: PerformanceMetrics[] = []
  
  // Test content loading (cold)
  const coldLoadMetric = await measurePerformance(
    'Cold Content Load',
    () => provider.getContent('blog', 'getting-started-with-shipany', 'en'),
    10
  )
  metrics.push(coldLoadMetric)
  console.log(`   ✅ Cold load: ${coldLoadMetric.averageTime.toFixed(2)}ms avg`)
  
  // Test content loading (warm)
  const warmLoadMetric = await measurePerformance(
    'Warm Content Load',
    () => provider.getContent('blog', 'getting-started-with-shipany', 'en'),
    100
  )
  metrics.push(warmLoadMetric)
  console.log(`   ✅ Warm load: ${warmLoadMetric.averageTime.toFixed(2)}ms avg`)
  
  // Test content list loading
  const listLoadMetric = await measurePerformance(
    'Content List Load',
    () => provider.getContentList('blog', 'en'),
    50
  )
  metrics.push(listLoadMetric)
  console.log(`   ✅ List load: ${listLoadMetric.averageTime.toFixed(2)}ms avg`)
  
  // Test content existence check
  const existsMetric = await measurePerformance(
    'Content Exists Check',
    () => provider.contentExists('blog', 'getting-started-with-shipany', 'en'),
    200
  )
  metrics.push(existsMetric)
  console.log(`   ✅ Exists check: ${existsMetric.averageTime.toFixed(2)}ms avg`)
  
  // Test title retrieval
  const titleMetric = await measurePerformance(
    'Title Retrieval',
    () => provider.getContentTitle('blog', 'getting-started-with-shipany', 'en'),
    200
  )
  metrics.push(titleMetric)
  console.log(`   ✅ Title retrieval: ${titleMetric.averageTime.toFixed(2)}ms avg`)
  
  // Test slug enumeration
  const slugsMetric = await measurePerformance(
    'Slugs Enumeration',
    () => provider.getAllContentSlugs('blog'),
    100
  )
  metrics.push(slugsMetric)
  console.log(`   ✅ Slugs enumeration: ${slugsMetric.averageTime.toFixed(2)}ms avg`)
  
  // Get cache stats
  const cacheStats = provider.getStats()
  
  // Generate recommendations
  const recommendations = generateRecommendations(metrics, cacheStats)
  
  return {
    provider: providerName,
    strategy: cacheStats.strategy,
    contentCount: 9, // Current content count
    metrics,
    cacheStats,
    recommendations
  }
}

/**
 * Test cache performance
 */
async function testCachePerformance() {
  console.log('\n💾 Testing Cache Performance...')
  
  const memoryCache = new MemoryCache(3600)
  const workersCache = new WorkersCache(3600)
  
  const testData = { content: 'test'.repeat(1000) } // ~4KB
  
  // Test MemoryCache
  console.log('\n   MemoryCache:')
  const memorySetMetric = await measurePerformance(
    'Memory Cache Set',
    () => memoryCache.set('test', testData),
    1000
  )
  console.log(`     Set: ${memorySetMetric.averageTime.toFixed(3)}ms avg`)
  
  const memoryGetMetric = await measurePerformance(
    'Memory Cache Get',
    () => memoryCache.get('test'),
    1000
  )
  console.log(`     Get: ${memoryGetMetric.averageTime.toFixed(3)}ms avg`)
  
  // Test WorkersCache
  console.log('\n   WorkersCache:')
  const workersSetMetric = await measurePerformance(
    'Workers Cache Set',
    () => workersCache.set('test', testData),
    100
  )
  console.log(`     Set: ${workersSetMetric.averageTime.toFixed(3)}ms avg`)
  
  const workersGetMetric = await measurePerformance(
    'Workers Cache Get',
    () => workersCache.get('test'),
    1000
  )
  console.log(`     Get: ${workersGetMetric.averageTime.toFixed(3)}ms avg`)
  
  return {
    memoryCache: {
      set: memorySetMetric,
      get: memoryGetMetric
    },
    workersCache: {
      set: workersSetMetric,
      get: workersGetMetric
    }
  }
}

/**
 * Generate performance recommendations
 */
function generateRecommendations(metrics: PerformanceMetrics[], cacheStats: any): string[] {
  const recommendations: string[] = []
  
  // Check cold vs warm load performance
  const coldLoad = metrics.find(m => m.operation === 'Cold Content Load')
  const warmLoad = metrics.find(m => m.operation === 'Warm Content Load')
  
  if (coldLoad && warmLoad) {
    const speedup = coldLoad.averageTime / warmLoad.averageTime
    if (speedup > 10) {
      recommendations.push(`Excellent cache performance: ${speedup.toFixed(1)}x speedup`)
    } else if (speedup > 3) {
      recommendations.push(`Good cache performance: ${speedup.toFixed(1)}x speedup`)
    } else {
      recommendations.push(`Cache performance could be improved: only ${speedup.toFixed(1)}x speedup`)
    }
  }
  
  // Check response times
  const avgResponseTime = metrics.reduce((sum, m) => sum + m.averageTime, 0) / metrics.length
  if (avgResponseTime < 1) {
    recommendations.push('Excellent response times (<1ms average)')
  } else if (avgResponseTime < 5) {
    recommendations.push('Good response times (<5ms average)')
  } else if (avgResponseTime < 20) {
    recommendations.push('Acceptable response times (<20ms average)')
  } else {
    recommendations.push('Response times need optimization (>20ms average)')
  }
  
  // Check memory usage
  const totalMemoryDelta = metrics.reduce((sum, m) => sum + m.memoryDelta, 0)
  if (totalMemoryDelta < 1024 * 1024) { // < 1MB
    recommendations.push('Low memory overhead (<1MB)')
  } else if (totalMemoryDelta < 5 * 1024 * 1024) { // < 5MB
    recommendations.push('Moderate memory overhead (<5MB)')
  } else {
    recommendations.push('High memory overhead (>5MB) - consider optimization')
  }
  
  // Check throughput
  const minThroughput = Math.min(...metrics.map(m => m.throughput))
  if (minThroughput > 1000) {
    recommendations.push('High throughput (>1000 ops/sec)')
  } else if (minThroughput > 100) {
    recommendations.push('Good throughput (>100 ops/sec)')
  } else {
    recommendations.push('Low throughput (<100 ops/sec) - needs optimization')
  }
  
  return recommendations
}

/**
 * Determine optimization thresholds
 */
function determineOptimizationThresholds(results: BaselineResults[]): {
  memoryThreshold: number
  responseTimeThreshold: number
  contentCountThreshold: number
  recommendations: string[]
} {
  const allMetrics = results.flatMap(r => r.metrics)
  const avgResponseTime = allMetrics.reduce((sum, m) => sum + m.averageTime, 0) / allMetrics.length
  const maxMemoryDelta = Math.max(...allMetrics.map(m => m.memoryDelta))
  
  const recommendations: string[] = []
  
  // Memory threshold: when cache overhead exceeds 10MB
  const memoryThreshold = 10 * 1024 * 1024 // 10MB
  if (maxMemoryDelta > memoryThreshold / 2) {
    recommendations.push('Consider implementing LRU cache when memory usage exceeds 10MB')
  }
  
  // Response time threshold: when average exceeds 50ms
  const responseTimeThreshold = 50 // ms
  if (avgResponseTime > responseTimeThreshold / 2) {
    recommendations.push('Consider cache optimization when response time exceeds 50ms')
  }
  
  // Content count threshold: estimate based on current performance
  const currentContentCount = 9
  const memoryPerItem = maxMemoryDelta / currentContentCount
  const contentCountThreshold = Math.floor(memoryThreshold / memoryPerItem)
  
  recommendations.push(`Current performance supports up to ~${contentCountThreshold} content items`)
  
  if (contentCountThreshold < 100) {
    recommendations.push('Consider implementing cache optimization before reaching 100 items')
  } else if (contentCountThreshold < 500) {
    recommendations.push('Consider implementing cache optimization before reaching 500 items')
  } else {
    recommendations.push('Current implementation scales well beyond 1000 items')
  }
  
  return {
    memoryThreshold,
    responseTimeThreshold,
    contentCountThreshold,
    recommendations
  }
}

/**
 * Main performance baseline function
 */
async function runPerformanceBaseline() {
  console.log('⚡ MDX Provider Performance Baseline Test')
  console.log('=========================================')
  
  // Test different configurations
  const results: BaselineResults[] = []
  
  // Test filesystem strategy
  results.push(await testMDXProviderPerformance('Filesystem Strategy', 'filesystem'))
  
  // Test precompiled strategy (if available)
  try {
    results.push(await testMDXProviderPerformance('Precompiled Strategy', 'precompiled'))
  } catch (error) {
    console.log('   ⚠️ Precompiled strategy not available in this environment')
  }
  
  // Test cache performance
  const cacheResults = await testCachePerformance()
  
  // Determine optimization thresholds
  const thresholds = determineOptimizationThresholds(results)
  
  // Summary
  console.log('\n📊 Performance Baseline Summary')
  console.log('===============================')
  
  results.forEach(result => {
    console.log(`\n${result.provider} (${result.strategy}):`)
    result.metrics.forEach(metric => {
      console.log(`   ${metric.operation}: ${metric.averageTime.toFixed(2)}ms avg (${metric.throughput.toFixed(0)} ops/sec)`)
    })
    console.log('   Recommendations:')
    result.recommendations.forEach(rec => console.log(`     - ${rec}`))
  })
  
  console.log('\n🎯 Optimization Thresholds:')
  console.log(`   Memory threshold: ${(thresholds.memoryThreshold / 1024 / 1024).toFixed(0)}MB`)
  console.log(`   Response time threshold: ${thresholds.responseTimeThreshold}ms`)
  console.log(`   Content count threshold: ~${thresholds.contentCountThreshold} items`)
  
  console.log('\n💡 Recommendations:')
  thresholds.recommendations.forEach(rec => console.log(`   - ${rec}`))
  
  return { results, cacheResults, thresholds }
}

// Run the baseline test
if (require.main === module) {
  runPerformanceBaseline().catch(console.error)
}

export { runPerformanceBaseline }
