/**
 * Simple Performance Test for MDX Provider
 * 
 * This script provides a focused performance analysis without
 * triggering complex build processes.
 */

import { MemoryCache } from '../src/services/content/providers/mdx/cache/memory-cache'
import { WorkersCache } from '../src/services/content/providers/mdx/cache/workers-cache'

interface TestResult {
  operation: string
  averageTime: number
  throughput: number
  memoryUsage: number
}

/**
 * Get memory usage in bytes
 */
function getMemoryUsage(): number {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    return process.memoryUsage().heapUsed
  }
  return 0
}

/**
 * Generate test data
 */
function generateTestData(size: number = 5000): any {
  return {
    slug: 'test-content',
    title: 'Test Content',
    lang: 'en',
    url: '/en/blogs/test-content',
    type: 'blog',
    description: 'Test description',
    body: {
      mdx: 'x'.repeat(size), // Variable size content
      component: undefined,
      html: undefined
    },
    coverImage: 'https://example.com/image.jpg',
    author: 'Test Author',
    publishedAt: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    featured: false,
    tags: ['test', 'performance']
  }
}

/**
 * Test cache performance
 */
async function testCachePerformance(
  cacheName: string,
  cache: MemoryCache | WorkersCache,
  iterations: number = 1000
): Promise<TestResult[]> {
  console.log(`\n🧪 Testing ${cacheName}...`)
  
  const testData = generateTestData()
  const results: TestResult[] = []
  
  // Test SET operations
  const setTimes: number[] = []
  const memoryBefore = getMemoryUsage()
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now()
    if (cache instanceof MemoryCache) {
      cache.set(`test-${i}`, testData)
    } else {
      await cache.set(`test-${i}`, testData)
    }
    const end = performance.now()
    setTimes.push(end - start)
  }
  
  const memoryAfter = getMemoryUsage()
  const avgSetTime = setTimes.reduce((sum, time) => sum + time, 0) / iterations
  
  results.push({
    operation: 'SET',
    averageTime: avgSetTime,
    throughput: 1000 / avgSetTime,
    memoryUsage: memoryAfter - memoryBefore
  })
  
  console.log(`   SET: ${avgSetTime.toFixed(3)}ms avg, ${(1000 / avgSetTime).toFixed(0)} ops/sec`)
  
  // Test GET operations
  const getTimes: number[] = []
  
  for (let i = 0; i < iterations; i++) {
    const randomIndex = Math.floor(Math.random() * iterations)
    const start = performance.now()
    if (cache instanceof MemoryCache) {
      cache.get(`test-${randomIndex}`)
    } else {
      await cache.get(`test-${randomIndex}`)
    }
    const end = performance.now()
    getTimes.push(end - start)
  }
  
  const avgGetTime = getTimes.reduce((sum, time) => sum + time, 0) / iterations
  
  results.push({
    operation: 'GET',
    averageTime: avgGetTime,
    throughput: 1000 / avgGetTime,
    memoryUsage: 0
  })
  
  console.log(`   GET: ${avgGetTime.toFixed(3)}ms avg, ${(1000 / avgGetTime).toFixed(0)} ops/sec`)
  
  // Test cache stats
  const stats = cache.getStats()
  console.log(`   Stats:`, stats)
  
  return results
}

/**
 * Test memory scaling
 */
async function testMemoryScaling() {
  console.log('\n📊 Testing Memory Scaling...')
  
  const itemCounts = [10, 50, 100, 250, 500]
  const results: { [key: number]: number } = {}
  
  for (const count of itemCounts) {
    // Force garbage collection if available
    if (global.gc) {
      global.gc()
    }
    
    const memoryBefore = getMemoryUsage()
    const cache = new MemoryCache(3600)
    
    // Fill cache
    for (let i = 0; i < count; i++) {
      const testData = generateTestData(Math.floor(Math.random() * 5000) + 2000) // 2-7KB per item
      cache.set(`item-${i}`, testData)
    }
    
    // Force garbage collection again
    if (global.gc) {
      global.gc()
    }
    
    const memoryAfter = getMemoryUsage()
    const memoryUsed = memoryAfter - memoryBefore
    results[count] = memoryUsed
    
    const avgPerItem = memoryUsed / count
    const cloudflarePercent = (memoryUsed / (128 * 1024 * 1024)) * 100
    
    console.log(`   ${count} items: ${(memoryUsed / 1024 / 1024).toFixed(2)}MB total, ${(avgPerItem / 1024).toFixed(2)}KB/item, ${cloudflarePercent.toFixed(2)}% of 128MB`)
  }
  
  return results
}

/**
 * Test cache efficiency
 */
async function testCacheEfficiency() {
  console.log('\n⚡ Testing Cache Efficiency...')
  
  const cache = new MemoryCache(300) // 5 minute TTL
  const testData = generateTestData()
  
  // Test cache hit/miss
  console.log('   Testing cache hit/miss patterns...')
  
  // Fill cache
  for (let i = 0; i < 100; i++) {
    cache.set(`item-${i}`, testData)
  }
  
  let hits = 0
  let misses = 0
  
  // Test random access pattern
  for (let i = 0; i < 1000; i++) {
    const randomKey = `item-${Math.floor(Math.random() * 150)}` // Some keys won't exist
    const result = cache.get(randomKey)
    if (result) {
      hits++
    } else {
      misses++
    }
  }
  
  const hitRate = (hits / (hits + misses)) * 100
  console.log(`   Hit rate: ${hitRate.toFixed(1)}% (${hits} hits, ${misses} misses)`)
  
  // Test TTL behavior
  console.log('   Testing TTL behavior...')
  const shortCache = new MemoryCache(1) // 1 second TTL
  shortCache.set('test', testData)
  
  const immediate = shortCache.get('test')
  console.log(`   Immediate retrieval: ${immediate ? 'Success' : 'Failed'}`)
  
  // Wait for expiration
  await new Promise(resolve => setTimeout(resolve, 1100))
  const afterExpiry = shortCache.get('test')
  console.log(`   After expiry: ${afterExpiry ? 'Failed (should be expired)' : 'Success (expired)'}`)
  
  return { hitRate, ttlWorking: !afterExpiry }
}

/**
 * Estimate scaling thresholds
 */
function estimateScalingThresholds(memoryResults: { [key: number]: number }) {
  console.log('\n🎯 Scaling Threshold Analysis...')
  
  const counts = Object.keys(memoryResults).map(Number).sort((a, b) => a - b)
  const memories = counts.map(count => memoryResults[count])
  
  // Calculate average memory per item
  const avgMemoryPerItem = memories.reduce((sum, mem, i) => sum + mem / counts[i], 0) / memories.length
  
  // Estimate thresholds
  const cloudflareLimit = 128 * 1024 * 1024 // 128MB
  const safeLimit = cloudflareLimit * 0.1 // Use only 10% for cache
  const warningLimit = cloudflareLimit * 0.25 // Warning at 25%
  
  const safeItemCount = Math.floor(safeLimit / avgMemoryPerItem)
  const warningItemCount = Math.floor(warningLimit / avgMemoryPerItem)
  const maxItemCount = Math.floor(cloudflareLimit / avgMemoryPerItem)
  
  console.log(`   Average memory per item: ${(avgMemoryPerItem / 1024).toFixed(2)}KB`)
  console.log(`   Safe item count (10% of 128MB): ~${safeItemCount} items`)
  console.log(`   Warning threshold (25% of 128MB): ~${warningItemCount} items`)
  console.log(`   Theoretical maximum (100% of 128MB): ~${maxItemCount} items`)
  
  // Recommendations
  console.log('\n💡 Recommendations:')
  if (safeItemCount > 1000) {
    console.log('   ✅ Current implementation can safely handle 1000+ items')
    console.log('   ✅ No immediate optimization needed')
  } else if (safeItemCount > 500) {
    console.log('   ⚠️ Consider optimization when approaching 500+ items')
    console.log('   ⚠️ Implement LRU cache for better memory management')
  } else {
    console.log('   ❌ Memory usage is high - optimization needed')
    console.log('   ❌ Implement compression and LRU cache immediately')
  }
  
  return {
    avgMemoryPerItem,
    safeItemCount,
    warningItemCount,
    maxItemCount
  }
}

/**
 * Main test function
 */
async function runSimplePerformanceTest() {
  console.log('⚡ MDX Provider Simple Performance Test')
  console.log('======================================')
  
  try {
    // Test cache implementations
    const memoryResults = await testCachePerformance('MemoryCache', new MemoryCache(3600), 1000)
    
    // Mock Cloudflare environment for WorkersCache
    ;(global as any).caches = {
      default: {
        match: async () => null,
        put: async () => {},
        delete: async () => true
      }
    }
    
    const workersResults = await testCachePerformance('WorkersCache', new WorkersCache(3600), 100)
    
    // Test memory scaling
    const memoryScaling = await testMemoryScaling()
    
    // Test cache efficiency
    const efficiency = await testCacheEfficiency()
    
    // Estimate scaling thresholds
    const thresholds = estimateScalingThresholds(memoryScaling)
    
    // Summary
    console.log('\n📈 Performance Summary')
    console.log('=====================')
    
    console.log('\nMemoryCache Performance:')
    memoryResults.forEach(result => {
      console.log(`   ${result.operation}: ${result.averageTime.toFixed(3)}ms avg, ${result.throughput.toFixed(0)} ops/sec`)
    })
    
    console.log('\nWorkersCache Performance:')
    workersResults.forEach(result => {
      console.log(`   ${result.operation}: ${result.averageTime.toFixed(3)}ms avg, ${result.throughput.toFixed(0)} ops/sec`)
    })
    
    console.log(`\nCache Efficiency: ${efficiency.hitRate.toFixed(1)}% hit rate, TTL working: ${efficiency.ttlWorking}`)
    
    console.log('\n🏁 Conclusion:')
    if (thresholds.safeItemCount > 1000) {
      console.log('   ✅ Current implementation is well-optimized for expected scale')
      console.log('   ✅ No immediate performance optimizations required')
      console.log('   ✅ Can safely handle growth to 1000+ content items')
    } else {
      console.log('   ⚠️ Performance optimization recommended before scaling')
      console.log('   ⚠️ Consider implementing the cache optimization plan')
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
if (require.main === module) {
  runSimplePerformanceTest().catch(console.error)
}

export { runSimplePerformanceTest }
