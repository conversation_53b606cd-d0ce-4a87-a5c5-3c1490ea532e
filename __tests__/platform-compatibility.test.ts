/**
 * Platform Compatibility Tests
 * 
 * Tests to ensure MDX Provider works correctly across different
 * deployment platforms (Cloudflare Workers and Vercel).
 */

import { MDXProvider } from '../src/services/content/providers/mdx'

// Mock different platform environments
const mockCloudflareEnvironment = () => {
  jest.doMock('../src/lib/platform', () => ({
    targetPlatform: 'cloudflare',
    isCloudflareEnvironment: () => true,
    isNodeEnvironment: () => false
  }))
  
  // Mock Cloudflare globals
  global.caches = {
    default: {
      match: jest.fn(),
      put: jest.fn(),
      delete: jest.fn()
    }
  } as any
  
  // Remove Node.js globals
  delete (global as any).process
}

const mockVercelEnvironment = () => {
  jest.doMock('../src/lib/platform', () => ({
    targetPlatform: 'node',
    isCloudflareEnvironment: () => false,
    isNodeEnvironment: () => true
  }))
  
  // Remove Cloudflare globals
  delete (global as any).caches
  
  // Restore Node.js globals
  global.process = process
}

describe('MDX Provider Platform Compatibility', () => {
  afterEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
  })
  
  describe('Cloudflare Workers Environment', () => {
    beforeEach(() => {
      mockCloudflareEnvironment()
    })
    
    it('should use precompiled strategy in Cloudflare environment', () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      const info = provider.getProviderInfo()
      
      expect(info.strategy).toBe('precompiled')
    })
    
    it('should handle memory constraints', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // Test that provider doesn't crash under memory pressure
      const promises = []
      for (let i = 0; i < 100; i++) {
        promises.push(provider.getContent('blog', `test-${i}`, 'en'))
      }
      
      const results = await Promise.all(promises)
      expect(results).toHaveLength(100)
    })
    
    it('should use Workers Cache API when available', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // Access the cache through the strategy
      const strategy = (provider as any).strategy
      expect(strategy.name).toBe('precompiled')
      
      // Test cache operations don't throw
      await expect(provider.getContent('blog', 'test', 'en')).resolves.not.toThrow()
    })
    
    it('should load content from static chunks', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // Mock static content chunks
      jest.doMock('../src/services/content/providers/mdx/static-content/static-content-blogs', () => ({
        default: {
          'blog-test-en': {
            slug: 'test',
            type: 'blog',
            locale: 'en',
            title: 'Test Blog',
            body: { mdx: '# Test Content' }
          }
        }
      }), { virtual: true })
      
      const content = await provider.getContent('blog', 'test', 'en')
      expect(content).toBeTruthy()
    })
  })
  
  describe('Vercel Environment', () => {
    beforeEach(() => {
      mockVercelEnvironment()
    })
    
    it('should use filesystem strategy in Vercel environment', () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      const info = provider.getProviderInfo()
      
      expect(info.strategy).toBe('filesystem')
    })
    
    it('should handle filesystem operations', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // Test that filesystem operations don't crash
      await expect(provider.getContent('blog', 'test', 'en')).resolves.not.toThrow()
      await expect(provider.getContentList('blog', 'en')).resolves.not.toThrow()
    })
    
    it('should use memory cache for performance', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // Access the cache through the strategy
      const strategy = (provider as any).strategy
      expect(strategy.name).toBe('filesystem')
      
      // Test multiple calls to same content (should use cache)
      const content1 = await provider.getContent('blog', 'test', 'en')
      const content2 = await provider.getContent('blog', 'test', 'en')
      
      // Both calls should complete without error
      expect(content1).toBe(content2)
    })
    
    it('should load content from filesystem index', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // Mock filesystem index
      jest.doMock('../src/services/content/providers/mdx/filesystem-index/content-index', () => ({
        contentIndex: {
          blogs: {
            en: [
              {
                slug: 'test',
                file: 'content/blog/en/test.mdx',
                lastModified: '2024-01-01T00:00:00Z',
                checksum: 'abc123'
              }
            ]
          }
        }
      }), { virtual: true })
      
      const slugs = await provider.getAllContentSlugs('blog')
      expect(Array.isArray(slugs)).toBe(true)
    })
  })
  
  describe('Cross-Platform Features', () => {
    const testCases = [
      { platform: 'cloudflare', strategy: 'precompiled' },
      { platform: 'node', strategy: 'filesystem' }
    ]
    
    testCases.forEach(({ platform, strategy }) => {
      describe(`${platform} platform`, () => {
        beforeEach(() => {
          if (platform === 'cloudflare') {
            mockCloudflareEnvironment()
          } else {
            mockVercelEnvironment()
          }
        })
        
        it(`should use ${strategy} strategy`, () => {
          const { MDXProvider } = require('../src/services/content/providers/mdx')
          const provider = new MDXProvider()
          const info = provider.getProviderInfo()
          
          expect(info.strategy).toBe(strategy)
        })
        
        it('should implement all ContentProvider methods', async () => {
          const { MDXProvider } = require('../src/services/content/providers/mdx')
          const provider = new MDXProvider()
          
          // Test all required methods exist and return expected types
          expect(typeof provider.getContent).toBe('function')
          expect(typeof provider.getContentList).toBe('function')
          expect(typeof provider.getContentForStaticGeneration).toBe('function')
          expect(typeof provider.getAllContentSlugs).toBe('function')
          expect(typeof provider.contentExists).toBe('function')
          expect(typeof provider.getContentTitle).toBe('function')
          expect(typeof provider.getContentMetadata).toBe('function')
          
          // Test methods return expected types
          await expect(provider.getContentList('blog', 'en')).resolves.toBeInstanceOf(Array)
          await expect(provider.getAllContentSlugs('blog')).resolves.toBeInstanceOf(Array)
          await expect(provider.contentExists('blog', 'test', 'en')).resolves.toEqual(expect.any(Boolean))
        })
        
        it('should handle all content types', async () => {
          const { MDXProvider } = require('../src/services/content/providers/mdx')
          const provider = new MDXProvider()
          
          const contentTypes = ['blog', 'product', 'case-study']
          
          for (const type of contentTypes) {
            await expect(provider.getContentList(type as any, 'en')).resolves.toBeInstanceOf(Array)
            await expect(provider.getAllContentSlugs(type as any)).resolves.toBeInstanceOf(Array)
          }
        })
        
        it('should support multiple locales', async () => {
          const { MDXProvider } = require('../src/services/content/providers/mdx')
          const provider = new MDXProvider()
          
          const locales = ['en', 'zh']
          
          for (const locale of locales) {
            await expect(provider.getContentList('blog', locale)).resolves.toBeInstanceOf(Array)
            await expect(provider.contentExists('blog', 'test', locale)).resolves.toEqual(expect.any(Boolean))
          }
        })
      })
    })
  })
  
  describe('Strategy Switching', () => {
    it('should respect forced strategy configuration', () => {
      // Test forcing precompiled strategy in Node environment
      mockVercelEnvironment()
      
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider({ forceStrategy: 'precompiled' })
      const info = provider.getProviderInfo()
      
      expect(info.strategy).toBe('precompiled')
    })
    
    it('should respect forced filesystem strategy in Cloudflare environment', () => {
      // Test forcing filesystem strategy in Cloudflare environment
      mockCloudflareEnvironment()
      
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider({ forceStrategy: 'filesystem' })
      const info = provider.getProviderInfo()
      
      expect(info.strategy).toBe('filesystem')
    })
  })
})
