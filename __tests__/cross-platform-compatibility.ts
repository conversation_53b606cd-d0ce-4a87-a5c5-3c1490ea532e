/**
 * Cross-Platform Compatibility Test for MDX Provider
 * 
 * This script tests the MDX Provider's behavior across different platforms
 * and validates the fallback mechanisms.
 */

import { MDXProvider } from '../src/services/content/providers/mdx/provider'
import { detectStrategy } from '../src/services/content/providers/mdx/utils/platform-detector'
import { MemoryCache } from '../src/services/content/providers/mdx/cache/memory-cache'
import { WorkersCache } from '../src/services/content/providers/mdx/cache/workers-cache'

interface PlatformTestResult {
  platform: string
  strategy: string
  cacheType: string
  cacheAPIAvailable: boolean
  providerInitialized: boolean
  contentLoadable: boolean
  performanceMetrics: {
    initTime: number
    firstLoadTime: number
    cachedLoadTime: number
  }
  errors: string[]
}

/**
 * Mock Cloudflare Workers environment
 */
function mockCloudflareEnvironment() {
  // Mock global caches object
  (global as any).caches = {
    default: {
      match: async (key: string) => null,
      put: async (key: string, response: Response) => {},
      delete: async (key: string) => true
    }
  }
  
  // Mock other Cloudflare-specific globals
  if (typeof (global as any).Request === 'undefined') {
    (global as any).Request = class MockRequest {}
  }

  if (typeof (global as any).Response === 'undefined') {
    (global as any).Response = class MockResponse {
      body: any
      headers: Map<string, any>

      constructor(body?: any, init?: any) {
        this.body = body
        this.headers = new Map()
        if (init?.headers) {
          Object.entries(init.headers).forEach(([key, value]) => {
            this.headers.set(key, value)
          })
        }
      }

      async json() {
        return JSON.parse(this.body)
      }
    }
  }
}

/**
 * Mock Vercel/Node.js environment
 */
function mockVercelEnvironment() {
  // Remove Cloudflare-specific globals
  delete (global as any).caches
  delete (global as any).Request
  delete (global as any).Response
  
  // Ensure Node.js globals are available
  if (typeof process === 'undefined') {
    (global as any).process = {
      env: { NODE_ENV: 'production' },
      memoryUsage: () => ({ heapUsed: 10 * 1024 * 1024 })
    }
  }
}

/**
 * Test platform detection
 */
function testPlatformDetection(): { cloudflare: string, vercel: string } {
  console.log('\n🔍 Testing Platform Detection...')
  
  // Test Cloudflare detection
  mockCloudflareEnvironment()
  const cloudflareStrategy = detectStrategy({})
  console.log(`   ✅ Cloudflare environment detected strategy: ${cloudflareStrategy}`)
  
  // Test Vercel detection
  mockVercelEnvironment()
  const vercelStrategy = detectStrategy({})
  console.log(`   ✅ Vercel environment detected strategy: ${vercelStrategy}`)
  
  return {
    cloudflare: cloudflareStrategy,
    vercel: vercelStrategy
  }
}

/**
 * Test cache implementations
 */
async function testCacheImplementations(): Promise<{
  memoryCache: boolean,
  workersCache: boolean,
  workersWithoutAPI: boolean
}> {
  console.log('\n💾 Testing Cache Implementations...')
  
  const results = {
    memoryCache: false,
    workersCache: false,
    workersWithoutAPI: false
  }
  
  try {
    // Test MemoryCache
    const memoryCache = new MemoryCache(300)
    memoryCache.set('test', { data: 'test' })
    const retrieved = memoryCache.get('test')
    results.memoryCache = retrieved !== null
    console.log(`   ✅ MemoryCache: ${results.memoryCache ? 'Working' : 'Failed'}`)
  } catch (error) {
    console.log(`   ❌ MemoryCache error: ${error}`)
  }
  
  try {
    // Test WorkersCache with Cache API
    mockCloudflareEnvironment()
    const workersCache = new WorkersCache(300)
    await workersCache.set('test', { data: 'test' })
    const retrieved = await workersCache.get('test')
    results.workersCache = retrieved !== null
    console.log(`   ✅ WorkersCache (with API): ${results.workersCache ? 'Working' : 'Failed'}`)
  } catch (error) {
    console.log(`   ❌ WorkersCache (with API) error: ${error}`)
  }
  
  try {
    // Test WorkersCache without Cache API
    mockVercelEnvironment()
    const workersCache = new WorkersCache(300)
    await workersCache.set('test', { data: 'test' })
    const retrieved = await workersCache.get('test')
    results.workersWithoutAPI = retrieved !== null
    console.log(`   ✅ WorkersCache (without API): ${results.workersWithoutAPI ? 'Working' : 'Failed'}`)
  } catch (error) {
    console.log(`   ❌ WorkersCache (without API) error: ${error}`)
  }
  
  return results
}

/**
 * Test MDX Provider on specific platform
 */
async function testMDXProviderOnPlatform(platformName: string, setupFn: () => void): Promise<PlatformTestResult> {
  console.log(`\n🧪 Testing MDX Provider on ${platformName}...`)
  
  const result: PlatformTestResult = {
    platform: platformName,
    strategy: '',
    cacheType: '',
    cacheAPIAvailable: false,
    providerInitialized: false,
    contentLoadable: false,
    performanceMetrics: {
      initTime: 0,
      firstLoadTime: 0,
      cachedLoadTime: 0
    },
    errors: []
  }
  
  try {
    // Setup platform environment
    setupFn()
    
    // Test platform detection
    result.strategy = detectStrategy({})
    result.cacheAPIAvailable = typeof (global as any).caches !== 'undefined'
    
    // Test provider initialization
    const initStart = Date.now()
    const provider = new MDXProvider({})
    result.providerInitialized = true
    result.performanceMetrics.initTime = Date.now() - initStart
    
    console.log(`   ✅ Provider initialized (${result.performanceMetrics.initTime}ms)`)
    console.log(`   ✅ Strategy: ${result.strategy}`)
    console.log(`   ✅ Cache API available: ${result.cacheAPIAvailable}`)
    
    // Test content loading (first time)
    try {
      const firstLoadStart = Date.now()
      const content = await provider.getContent('blog', 'getting-started-with-shipany', 'en')
      result.performanceMetrics.firstLoadTime = Date.now() - firstLoadStart
      result.contentLoadable = content !== null
      
      console.log(`   ✅ Content loadable: ${result.contentLoadable} (${result.performanceMetrics.firstLoadTime}ms)`)
      
      if (content) {
        // Test cached loading
        const cachedLoadStart = Date.now()
        const cachedContent = await provider.getContent('blog', 'getting-started-with-shipany', 'en')
        result.performanceMetrics.cachedLoadTime = Date.now() - cachedLoadStart
        
        console.log(`   ✅ Cached load time: ${result.performanceMetrics.cachedLoadTime}ms`)
        
        // Test other operations
        const exists = await provider.contentExists('blog', 'getting-started-with-shipany', 'en')
        console.log(`   ✅ Content exists check: ${exists}`)
        
        const title = await provider.getContentTitle('blog', 'getting-started-with-shipany', 'en')
        console.log(`   ✅ Content title: ${title ? 'Retrieved' : 'Failed'}`)
        
        const slugs = await provider.getAllContentSlugs('blog')
        console.log(`   ✅ All slugs: ${slugs.length} items`)
      }
    } catch (error) {
      result.errors.push(`Content loading error: ${error}`)
      console.log(`   ❌ Content loading failed: ${error}`)
    }
    
  } catch (error) {
    result.errors.push(`Provider initialization error: ${error}`)
    console.log(`   ❌ Provider initialization failed: ${error}`)
  }
  
  return result
}

/**
 * Test fallback mechanisms
 */
async function testFallbackMechanisms() {
  console.log('\n🔄 Testing Fallback Mechanisms...')
  
  // Test Cache API fallback
  console.log('\n   Testing Cache API fallback...')
  mockCloudflareEnvironment()
  
  // Break the Cache API
  const originalCaches = (global as any).caches
  ;(global as any).caches = {
    default: {
      match: async () => { throw new Error('Cache API error') },
      put: async () => { throw new Error('Cache API error') },
      delete: async () => { throw new Error('Cache API error') }
    }
  }
  
  try {
    const workersCache = new WorkersCache(300)
    await workersCache.set('test', { data: 'test' })
    const retrieved = await workersCache.get('test')
    console.log(`   ✅ Cache API fallback: ${retrieved ? 'Working (memory fallback)' : 'Failed'}`)
  } catch (error) {
    console.log(`   ❌ Cache API fallback error: ${error}`)
  }
  
  // Restore Cache API
  ;(global as any).caches = originalCaches
  
  // Test strategy fallback
  console.log('\n   Testing strategy fallback...')
  try {
    // Force filesystem strategy in Cloudflare environment
    const provider = new MDXProvider({ forceStrategy: 'filesystem' })
    console.log(`   ✅ Strategy override: Working`)
  } catch (error) {
    console.log(`   ❌ Strategy override error: ${error}`)
  }
}

/**
 * Main compatibility test function
 */
async function runCompatibilityTests() {
  console.log('🌐 MDX Provider Cross-Platform Compatibility Test')
  console.log('================================================')
  
  // Test platform detection
  const platformDetection = testPlatformDetection()
  
  // Test cache implementations
  const cacheTests = await testCacheImplementations()
  
  // Test MDX Provider on different platforms
  const cloudflareResult = await testMDXProviderOnPlatform(
    'Cloudflare Workers',
    mockCloudflareEnvironment
  )
  
  const vercelResult = await testMDXProviderOnPlatform(
    'Vercel/Node.js',
    mockVercelEnvironment
  )
  
  // Test fallback mechanisms
  await testFallbackMechanisms()
  
  // Summary
  console.log('\n📊 Compatibility Test Summary')
  console.log('=============================')
  
  console.log('\n🔍 Platform Detection:')
  console.log(`   Cloudflare: ${platformDetection.cloudflare}`)
  console.log(`   Vercel: ${platformDetection.vercel}`)
  
  console.log('\n💾 Cache Implementations:')
  console.log(`   MemoryCache: ${cacheTests.memoryCache ? '✅' : '❌'}`)
  console.log(`   WorkersCache (with API): ${cacheTests.workersCache ? '✅' : '❌'}`)
  console.log(`   WorkersCache (without API): ${cacheTests.workersWithoutAPI ? '✅' : '❌'}`)
  
  console.log('\n🧪 Platform Tests:')
  console.log(`   Cloudflare Workers:`)
  console.log(`     Strategy: ${cloudflareResult.strategy}`)
  console.log(`     Initialized: ${cloudflareResult.providerInitialized ? '✅' : '❌'}`)
  console.log(`     Content Loadable: ${cloudflareResult.contentLoadable ? '✅' : '❌'}`)
  console.log(`     Init Time: ${cloudflareResult.performanceMetrics.initTime}ms`)
  console.log(`     First Load: ${cloudflareResult.performanceMetrics.firstLoadTime}ms`)
  console.log(`     Cached Load: ${cloudflareResult.performanceMetrics.cachedLoadTime}ms`)
  
  console.log(`   Vercel/Node.js:`)
  console.log(`     Strategy: ${vercelResult.strategy}`)
  console.log(`     Initialized: ${vercelResult.providerInitialized ? '✅' : '❌'}`)
  console.log(`     Content Loadable: ${vercelResult.contentLoadable ? '✅' : '❌'}`)
  console.log(`     Init Time: ${vercelResult.performanceMetrics.initTime}ms`)
  console.log(`     First Load: ${vercelResult.performanceMetrics.firstLoadTime}ms`)
  console.log(`     Cached Load: ${vercelResult.performanceMetrics.cachedLoadTime}ms`)
  
  // Error summary
  const allErrors = [...cloudflareResult.errors, ...vercelResult.errors]
  if (allErrors.length > 0) {
    console.log('\n❌ Errors Found:')
    allErrors.forEach(error => console.log(`   - ${error}`))
  } else {
    console.log('\n✅ No errors found - Full compatibility achieved!')
  }
  
  return {
    platformDetection,
    cacheTests,
    cloudflareResult,
    vercelResult,
    allErrors
  }
}

// Run the tests
if (require.main === module) {
  runCompatibilityTests().catch(console.error)
}

export { runCompatibilityTests }
