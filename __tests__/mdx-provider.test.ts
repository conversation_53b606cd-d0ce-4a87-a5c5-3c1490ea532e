/**
 * MDX Provider Tests
 * 
 * Comprehensive test suite for the MDX content provider,
 * covering both platform strategies and core functionality.
 */

import { MDXProvider } from '../src/services/content/providers/mdx'
import type { MDXProviderConfig } from '../src/services/content/providers/mdx/types'

// Mock platform detection
jest.mock('../src/lib/platform', () => ({
  targetPlatform: 'node',
  isCloudflareEnvironment: () => false,
  isNodeEnvironment: () => true
}))

describe('MDXProvider', () => {
  let provider: MDXProvider
  
  beforeEach(() => {
    provider = new MDXProvider()
  })
  
  afterEach(() => {
    jest.clearAllMocks()
  })
  
  describe('Initialization', () => {
    it('should initialize with default configuration', () => {
      const provider = new MDXProvider()
      expect(provider.name).toBe('mdx')
      expect(provider.version).toBe('1.0.0')
    })
    
    it('should accept custom configuration', () => {
      const config: Partial<MDXProviderConfig> = {
        cacheTTL: 7200,
        developmentMode: false
      }
      
      const provider = new MDXProvider(config)
      const info = provider.getProviderInfo()
      
      expect(info.config.cacheTTL).toBe(7200)
      expect(info.config.developmentMode).toBe(false)
    })
    
    it('should select filesystem strategy for Node.js environment', () => {
      const provider = new MDXProvider()
      const info = provider.getProviderInfo()
      
      expect(info.strategy).toBe('filesystem')
    })
    
    it('should respect forced strategy configuration', () => {
      const config: Partial<MDXProviderConfig> = {
        forceStrategy: 'precompiled'
      }
      
      const provider = new MDXProvider(config)
      const info = provider.getProviderInfo()
      
      expect(info.strategy).toBe('precompiled')
    })
  })
  
  describe('Content Operations', () => {
    it('should handle getContent gracefully when content not found', async () => {
      const content = await provider.getContent('blog', 'non-existent', 'en')
      expect(content).toBeNull()
    })
    
    it('should handle getContentList gracefully', async () => {
      const contentList = await provider.getContentList('blog', 'en')
      expect(Array.isArray(contentList)).toBe(true)
    })
    
    it('should validate content type', async () => {
      const content = await provider.getContent('invalid-type' as any, 'test', 'en')
      expect(content).toBeNull()
    })
    
    it('should validate locale', async () => {
      const content = await provider.getContent('blog', 'test', 'invalid-locale')
      expect(content).toBeNull()
    })
    
    it('should handle contentExists check', async () => {
      const exists = await provider.contentExists('blog', 'test', 'en')
      expect(typeof exists).toBe('boolean')
    })
    
    it('should handle getContentTitle', async () => {
      const title = await provider.getContentTitle('blog', 'test', 'en')
      expect(title === null || typeof title === 'string').toBe(true)
    })
    
    it('should handle getContentMetadata', async () => {
      const metadata = await provider.getContentMetadata('blog', 'test', 'en')
      expect(metadata === null || typeof metadata === 'object').toBe(true)
    })
  })
  
  describe('Static Generation', () => {
    it('should get content for static generation', async () => {
      const content = await provider.getContentForStaticGeneration('blog')
      expect(Array.isArray(content)).toBe(true)
    })
    
    it('should get all content slugs', async () => {
      const slugs = await provider.getAllContentSlugs('blog')
      expect(Array.isArray(slugs)).toBe(true)
      
      // Check structure if slugs exist
      if (slugs.length > 0) {
        expect(slugs[0]).toHaveProperty('locale')
        expect(slugs[0]).toHaveProperty('slug')
      }
    })
    
    it('should get language versions', async () => {
      const versions = await provider.getLanguageVersions('blog', 'test')
      expect(Array.isArray(versions)).toBe(true)
    })
  })
  
  describe('Error Handling', () => {
    it('should handle errors gracefully in getContent', async () => {
      // Mock strategy to throw error
      const mockStrategy = {
        name: 'mock',
        getContent: jest.fn().mockRejectedValue(new Error('Test error'))
      }
      
      // Replace strategy
      ;(provider as any).strategy = mockStrategy
      
      const content = await provider.getContent('blog', 'test', 'en')
      expect(content).toBeNull()
    })
    
    it('should handle errors gracefully in getContentList', async () => {
      // Mock strategy to throw error
      const mockStrategy = {
        name: 'mock',
        getContentList: jest.fn().mockRejectedValue(new Error('Test error'))
      }
      
      // Replace strategy
      ;(provider as any).strategy = mockStrategy
      
      const contentList = await provider.getContentList('blog', 'en')
      expect(contentList).toEqual([])
    })
  })
  
  describe('Provider Information', () => {
    it('should return provider information', () => {
      const info = provider.getProviderInfo()
      
      expect(info).toHaveProperty('name', 'mdx')
      expect(info).toHaveProperty('version', '1.0.0')
      expect(info).toHaveProperty('strategy')
      expect(info).toHaveProperty('config')
    })
    
    it('should return provider statistics', () => {
      const stats = provider.getStats()
      
      expect(stats).toHaveProperty('provider', 'mdx')
      expect(stats).toHaveProperty('strategy')
      expect(stats).toHaveProperty('version', '1.0.0')
    })
  })
  
  describe('Configuration Validation', () => {
    it('should throw error for invalid cacheTTL', () => {
      expect(() => {
        new MDXProvider({ cacheTTL: -1 })
      }).toThrow('cacheTTL must be a positive number')
    })
    
    it('should throw error for invalid forceStrategy', () => {
      expect(() => {
        new MDXProvider({ forceStrategy: 'invalid' as any })
      }).toThrow('forceStrategy must be either "precompiled" or "filesystem"')
    })
    
    it('should throw error for invalid contentDir', () => {
      expect(() => {
        new MDXProvider({ contentDir: 123 as any })
      }).toThrow('contentDir must be a string')
    })
  })
})
