/**
 * Performance Tests for MDX Provider
 * 
 * Tests to ensure MDX Provider meets performance requirements
 * across different platforms and usage scenarios.
 */

import { MDXProvider } from '../src/services/content/providers/mdx'

// Performance test utilities
const measureTime = async (fn: () => Promise<any>): Promise<number> => {
  const start = performance.now()
  await fn()
  const end = performance.now()
  return end - start
}

const measureMemory = (): number => {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    return process.memoryUsage().heapUsed
  }
  return 0
}

describe('MDX Provider Performance', () => {
  let provider: MDXProvider
  
  beforeEach(() => {
    provider = new MDXProvider()
  })
  
  describe('Content Loading Performance', () => {
    it('should load single content item within acceptable time', async () => {
      const loadTime = await measureTime(async () => {
        await provider.getContent('blog', 'test-post', 'en')
      })
      
      // Should load within 200ms
      expect(loadTime).toBeLessThan(200)
    })
    
    it('should load content list within acceptable time', async () => {
      const loadTime = await measureTime(async () => {
        await provider.getContentList('blog', 'en', { limit: 10 })
      })
      
      // Should load within 500ms
      expect(loadTime).toBeLessThan(500)
    })
    
    it('should benefit from caching on subsequent requests', async () => {
      // First request
      const firstLoadTime = await measureTime(async () => {
        await provider.getContent('blog', 'test-post', 'en')
      })
      
      // Second request (should be cached)
      const secondLoadTime = await measureTime(async () => {
        await provider.getContent('blog', 'test-post', 'en')
      })
      
      // Second request should be significantly faster
      expect(secondLoadTime).toBeLessThan(firstLoadTime * 0.5)
    })
  })
  
  describe('Memory Usage', () => {
    it('should not leak memory with repeated requests', async () => {
      const initialMemory = measureMemory()
      
      // Make many requests
      for (let i = 0; i < 100; i++) {
        await provider.getContent('blog', `test-${i % 10}`, 'en')
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      const finalMemory = measureMemory()
      const memoryIncrease = finalMemory - initialMemory
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024)
    })
    
    it('should handle concurrent requests efficiently', async () => {
      const initialMemory = measureMemory()
      
      // Create many concurrent requests
      const promises = []
      for (let i = 0; i < 50; i++) {
        promises.push(provider.getContent('blog', `test-${i % 5}`, 'en'))
      }
      
      await Promise.all(promises)
      
      const finalMemory = measureMemory()
      const memoryIncrease = finalMemory - initialMemory
      
      // Memory increase should be reasonable
      expect(memoryIncrease).toBeLessThan(30 * 1024 * 1024)
    })
  })
  
  describe('Cloudflare Workers Specific Performance', () => {
    beforeEach(() => {
      // Mock Cloudflare environment
      jest.doMock('../src/lib/platform', () => ({
        targetPlatform: 'cloudflare',
        isCloudflareEnvironment: () => true,
        isNodeEnvironment: () => false
      }))
      
      global.caches = {
        default: {
          match: jest.fn().mockResolvedValue(null),
          put: jest.fn().mockResolvedValue(undefined),
          delete: jest.fn().mockResolvedValue(true)
        }
      } as any
    })
    
    afterEach(() => {
      jest.resetModules()
      delete (global as any).caches
    })
    
    it('should meet Cloudflare Workers cold start requirements', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      const coldStartTime = await measureTime(async () => {
        await provider.getContent('blog', 'test-post', 'en')
      })
      
      // Cold start should be under 100ms for Workers
      expect(coldStartTime).toBeLessThan(100)
    })
    
    it('should handle memory constraints in Workers environment', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // Simulate loading many content items
      const promises = []
      for (let i = 0; i < 20; i++) {
        promises.push(provider.getContentList('blog', 'en', { limit: 5 }))
      }
      
      // Should not throw memory errors
      await expect(Promise.all(promises)).resolves.not.toThrow()
    })
    
    it('should use chunked loading efficiently', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // Mock static content chunks
      jest.doMock('../src/services/content/providers/mdx/static-content/static-content-blogs', () => ({
        default: generateMockContent('blog', 50)
      }), { virtual: true })
      
      const loadTime = await measureTime(async () => {
        await provider.getContentList('blog', 'en')
      })
      
      // Chunked loading should be efficient
      expect(loadTime).toBeLessThan(150)
    })
  })
  
  describe('Vercel Environment Performance', () => {
    beforeEach(() => {
      // Mock Vercel environment
      jest.doMock('../src/lib/platform', () => ({
        targetPlatform: 'node',
        isCloudflareEnvironment: () => false,
        isNodeEnvironment: () => true
      }))
    })
    
    afterEach(() => {
      jest.resetModules()
    })
    
    it('should leverage filesystem caching effectively', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // First load
      const firstLoadTime = await measureTime(async () => {
        await provider.getContentList('blog', 'en')
      })
      
      // Second load (should use cache)
      const secondLoadTime = await measureTime(async () => {
        await provider.getContentList('blog', 'en')
      })
      
      // Cache should provide significant speedup
      expect(secondLoadTime).toBeLessThan(firstLoadTime * 0.3)
    })
    
    it('should handle large content sets efficiently', async () => {
      const { MDXProvider } = require('../src/services/content/providers/mdx')
      const provider = new MDXProvider()
      
      // Mock large content index
      jest.doMock('../src/services/content/providers/mdx/filesystem-index/content-index', () => ({
        contentIndex: {
          blogs: {
            en: Array.from({ length: 1000 }, (_, i) => ({
              slug: `post-${i}`,
              file: `content/blog/en/post-${i}.mdx`,
              lastModified: '2024-01-01T00:00:00Z',
              checksum: `checksum-${i}`
            }))
          }
        }
      }), { virtual: true })
      
      const loadTime = await measureTime(async () => {
        await provider.getAllContentSlugs('blog')
      })
      
      // Should handle large datasets efficiently
      expect(loadTime).toBeLessThan(300)
    })
  })
  
  describe('Comparative Performance', () => {
    it('should perform comparably to other providers', async () => {
      // This would require actual comparison with other providers
      // For now, we test that performance is within acceptable bounds
      
      const operations = [
        () => provider.getContent('blog', 'test', 'en'),
        () => provider.getContentList('blog', 'en', { limit: 10 }),
        () => provider.getAllContentSlugs('blog'),
        () => provider.contentExists('blog', 'test', 'en')
      ]
      
      for (const operation of operations) {
        const time = await measureTime(operation)
        expect(time).toBeLessThan(200) // All operations under 200ms
      }
    })
  })
  
  describe('Stress Testing', () => {
    it('should handle high concurrent load', async () => {
      const concurrentRequests = 100
      const promises = []
      
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(provider.getContent('blog', `test-${i % 10}`, 'en'))
      }
      
      const startTime = performance.now()
      const results = await Promise.all(promises)
      const endTime = performance.now()
      
      const totalTime = endTime - startTime
      const averageTime = totalTime / concurrentRequests
      
      expect(results).toHaveLength(concurrentRequests)
      expect(averageTime).toBeLessThan(50) // Average under 50ms per request
    })
    
    it('should maintain performance under sustained load', async () => {
      const iterations = 10
      const requestsPerIteration = 20
      const times: number[] = []
      
      for (let i = 0; i < iterations; i++) {
        const iterationTime = await measureTime(async () => {
          const promises = []
          for (let j = 0; j < requestsPerIteration; j++) {
            promises.push(provider.getContent('blog', `test-${j}`, 'en'))
          }
          await Promise.all(promises)
        })
        
        times.push(iterationTime)
      }
      
      // Performance should not degrade significantly over time
      const firstHalf = times.slice(0, iterations / 2)
      const secondHalf = times.slice(iterations / 2)
      
      const firstHalfAvg = firstHalf.reduce((a, b) => a + b) / firstHalf.length
      const secondHalfAvg = secondHalf.reduce((a, b) => a + b) / secondHalf.length
      
      // Second half should not be more than 50% slower than first half
      expect(secondHalfAvg).toBeLessThan(firstHalfAvg * 1.5)
    })
  })
})

// Helper function to generate mock content
function generateMockContent(type: string, count: number) {
  const content: Record<string, any> = {}
  
  for (let i = 0; i < count; i++) {
    const key = `${type}-test-${i}-en`
    content[key] = {
      slug: `test-${i}`,
      type,
      locale: 'en',
      title: `Test ${type} ${i}`,
      body: { mdx: `# Test Content ${i}` }
    }
  }
  
  return content
}
