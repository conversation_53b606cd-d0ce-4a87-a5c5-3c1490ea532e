/**
 * MDX Provider Integration Tests
 * 
 * Tests the complete integration of MDX Provider with both strategies
 * using real content files and build artifacts.
 */

import { MDXProvider } from '../src/services/content/providers/mdx'

describe('MDX Provider Integration', () => {
  describe('Precompiled Strategy (Cloudflare Workers)', () => {
    let provider: MDXProvider
    
    beforeEach(() => {
      // Force precompiled strategy
      provider = new MDXProvider({ forceStrategy: 'precompiled' })
    })
    
    it('should use precompiled strategy', () => {
      const info = provider.getProviderInfo()
      expect(info.strategy).toBe('precompiled')
    })
    
    it('should load content from static chunks', async () => {
      const content = await provider.getContent('blog', 'getting-started-with-shipany', 'en')
      
      expect(content).toBeTruthy()
      expect(content?.slug).toBe('getting-started-with-shipany')
      expect(content?.type).toBe('blog')
      expect(content?.locale).toBe('en')
      expect(content?.title).toBeTruthy()
      expect(content?.body?.mdx).toBeTruthy()
    })
    
    it('should list content from static chunks', async () => {
      const contentList = await provider.getContentList('blog', 'en')
      
      expect(Array.isArray(contentList)).toBe(true)
      expect(contentList.length).toBeGreaterThan(0)
      
      // Check structure of first item
      if (contentList.length > 0) {
        const firstItem = contentList[0]
        expect(firstItem).toHaveProperty('slug')
        expect(firstItem).toHaveProperty('type', 'blog')
        expect(firstItem).toHaveProperty('locale', 'en')
        expect(firstItem).toHaveProperty('title')
        expect(firstItem).toHaveProperty('body')
        expect(firstItem.body).toHaveProperty('mdx')
      }
    })
    
    it('should get all content slugs', async () => {
      const slugs = await provider.getAllContentSlugs('blog')
      
      expect(Array.isArray(slugs)).toBe(true)
      expect(slugs.length).toBeGreaterThan(0)
      
      // Check structure
      if (slugs.length > 0) {
        const firstSlug = slugs[0]
        expect(firstSlug).toHaveProperty('locale')
        expect(firstSlug).toHaveProperty('slug')
        expect(['en', 'zh']).toContain(firstSlug.locale)
      }
    })
    
    it('should check content existence', async () => {
      const exists = await provider.contentExists('blog', 'getting-started-with-shipany', 'en')
      expect(exists).toBe(true)
      
      const notExists = await provider.contentExists('blog', 'non-existent-post', 'en')
      expect(notExists).toBe(false)
    })
    
    it('should get content metadata', async () => {
      const metadata = await provider.getContentMetadata('blog', 'getting-started-with-shipany', 'en')
      
      expect(metadata).toBeTruthy()
      expect(metadata).toHaveProperty('title')
      expect(metadata).toHaveProperty('description')
      expect(metadata).toHaveProperty('publishedAt')
    })
  })
  
  describe('Filesystem Strategy (Vercel/Node.js)', () => {
    let provider: MDXProvider
    
    beforeEach(() => {
      // Force filesystem strategy
      provider = new MDXProvider({ forceStrategy: 'filesystem' })
    })
    
    it('should use filesystem strategy', () => {
      const info = provider.getProviderInfo()
      expect(info.strategy).toBe('filesystem')
    })
    
    it('should load content from filesystem', async () => {
      const content = await provider.getContent('blog', 'getting-started-with-shipany', 'en')
      
      expect(content).toBeTruthy()
      expect(content?.slug).toBe('getting-started-with-shipany')
      expect(content?.type).toBe('blog')
      expect(content?.locale).toBe('en')
      expect(content?.title).toBeTruthy()
      expect(content?.body?.mdx).toBeTruthy()
    })
    
    it('should list content from filesystem', async () => {
      const contentList = await provider.getContentList('blog', 'en')
      
      expect(Array.isArray(contentList)).toBe(true)
      expect(contentList.length).toBeGreaterThan(0)
      
      // Check structure of first item
      if (contentList.length > 0) {
        const firstItem = contentList[0]
        expect(firstItem).toHaveProperty('slug')
        expect(firstItem).toHaveProperty('type', 'blog')
        expect(firstItem).toHaveProperty('locale', 'en')
        expect(firstItem).toHaveProperty('title')
        expect(firstItem).toHaveProperty('body')
        expect(firstItem.body).toHaveProperty('mdx')
      }
    })
    
    it('should handle all content types', async () => {
      const contentTypes = ['blog', 'product', 'case-study'] as const
      
      for (const type of contentTypes) {
        const contentList = await provider.getContentList(type, 'en')
        expect(Array.isArray(contentList)).toBe(true)
        
        const slugs = await provider.getAllContentSlugs(type)
        expect(Array.isArray(slugs)).toBe(true)
      }
    })
  })
  
  describe('Cross-Strategy Consistency', () => {
    let precompiledProvider: MDXProvider
    let filesystemProvider: MDXProvider
    
    beforeEach(() => {
      precompiledProvider = new MDXProvider({ forceStrategy: 'precompiled' })
      filesystemProvider = new MDXProvider({ forceStrategy: 'filesystem' })
    })
    
    it('should return consistent content between strategies', async () => {
      const precompiledContent = await precompiledProvider.getContent('blog', 'getting-started-with-shipany', 'en')
      const filesystemContent = await filesystemProvider.getContent('blog', 'getting-started-with-shipany', 'en')
      
      expect(precompiledContent).toBeTruthy()
      expect(filesystemContent).toBeTruthy()
      
      // Compare key properties
      expect(precompiledContent?.slug).toBe(filesystemContent?.slug)
      expect(precompiledContent?.type).toBe(filesystemContent?.type)
      expect(precompiledContent?.locale).toBe(filesystemContent?.locale)
      expect(precompiledContent?.title).toBe(filesystemContent?.title)
      expect(precompiledContent?.body?.mdx).toBe(filesystemContent?.body?.mdx)
    })
    
    it('should return consistent content lists between strategies', async () => {
      const precompiledList = await precompiledProvider.getContentList('blog', 'en')
      const filesystemList = await filesystemProvider.getContentList('blog', 'en')
      
      expect(precompiledList.length).toBe(filesystemList.length)
      
      // Sort both lists by slug for comparison
      const sortedPrecompiled = precompiledList.sort((a, b) => a.slug.localeCompare(b.slug))
      const sortedFilesystem = filesystemList.sort((a, b) => a.slug.localeCompare(b.slug))
      
      for (let i = 0; i < sortedPrecompiled.length; i++) {
        expect(sortedPrecompiled[i].slug).toBe(sortedFilesystem[i].slug)
        expect(sortedPrecompiled[i].title).toBe(sortedFilesystem[i].title)
      }
    })
    
    it('should return consistent slugs between strategies', async () => {
      const precompiledSlugs = await precompiledProvider.getAllContentSlugs('blog')
      const filesystemSlugs = await filesystemProvider.getAllContentSlugs('blog')
      
      expect(precompiledSlugs.length).toBe(filesystemSlugs.length)
      
      // Sort both arrays for comparison
      const sortedPrecompiled = precompiledSlugs.sort((a, b) => `${a.locale}-${a.slug}`.localeCompare(`${b.locale}-${b.slug}`))
      const sortedFilesystem = filesystemSlugs.sort((a, b) => `${a.locale}-${a.slug}`.localeCompare(`${b.locale}-${b.slug}`))
      
      for (let i = 0; i < sortedPrecompiled.length; i++) {
        expect(sortedPrecompiled[i].locale).toBe(sortedFilesystem[i].locale)
        expect(sortedPrecompiled[i].slug).toBe(sortedFilesystem[i].slug)
      }
    })
  })
  
  describe('Provider Configuration', () => {
    it('should accept custom configuration', () => {
      const provider = new MDXProvider({
        cacheTTL: 7200,
        developmentMode: false,
        contentDir: './custom-content'
      })
      
      const info = provider.getProviderInfo()
      expect(info.config.cacheTTL).toBe(7200)
      expect(info.config.developmentMode).toBe(false)
      expect(info.config.contentDir).toBe('./custom-content')
    })
    
    it('should provide provider statistics', () => {
      const provider = new MDXProvider()
      const stats = provider.getStats()
      
      expect(stats).toHaveProperty('provider', 'mdx')
      expect(stats).toHaveProperty('strategy')
      expect(stats).toHaveProperty('version', '1.0.0')
    })
  })
})
