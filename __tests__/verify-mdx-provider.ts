#!/usr/bin/env npx tsx

/**
 * MDX Provider Verification Script
 * 
 * Simple script to verify that the MDX Provider is working correctly
 * with both strategies.
 */

import { MDXProvider } from '../src/services/content/providers/mdx'

async function verifyMDXProvider() {
  console.log('🧪 Verifying MDX Provider Implementation...\n')
  
  try {
    // Test 1: Precompiled Strategy (Cloudflare Workers)
    console.log('📦 Testing Precompiled Strategy (Cloudflare Workers)...')
    const precompiledProvider = new MDXProvider({ forceStrategy: 'precompiled' })
    
    const precompiledInfo = precompiledProvider.getProviderInfo()
    console.log(`   ✅ Strategy: ${precompiledInfo.strategy}`)
    
    const precompiledContent = await precompiledProvider.getContent('blog', 'getting-started-with-shipany', 'en')
    console.log(`   ✅ Content loaded: ${precompiledContent ? 'Yes' : 'No'}`)
    if (precompiledContent) {
      console.log(`   ✅ Title: ${precompiledContent.title}`)
      console.log(`   ✅ MDX content length: ${(precompiledContent.body as any).mdx.length} chars`)
    }
    
    const precompiledList = await precompiledProvider.getContentList('blog', 'en')
    console.log(`   ✅ Blog list count: ${precompiledList.length}`)
    
    const precompiledSlugs = await precompiledProvider.getAllContentSlugs('blog')
    console.log(`   ✅ Total blog slugs: ${precompiledSlugs.length}`)
    
    // Test 2: Filesystem Strategy (Vercel/Node.js)
    console.log('\n📁 Testing Filesystem Strategy (Vercel/Node.js)...')
    const filesystemProvider = new MDXProvider({ forceStrategy: 'filesystem' })
    
    const filesystemInfo = filesystemProvider.getProviderInfo()
    console.log(`   ✅ Strategy: ${filesystemInfo.strategy}`)
    
    const filesystemContent = await filesystemProvider.getContent('blog', 'getting-started-with-shipany', 'en')
    console.log(`   ✅ Content loaded: ${filesystemContent ? 'Yes' : 'No'}`)
    if (filesystemContent) {
      console.log(`   ✅ Title: ${filesystemContent.title}`)
      console.log(`   ✅ MDX content length: ${(filesystemContent.body as any).mdx.length} chars`)
    }
    
    const filesystemList = await filesystemProvider.getContentList('blog', 'en')
    console.log(`   ✅ Blog list count: ${filesystemList.length}`)
    
    const filesystemSlugs = await filesystemProvider.getAllContentSlugs('blog')
    console.log(`   ✅ Total blog slugs: ${filesystemSlugs.length}`)
    
    // Test 3: Cross-Strategy Consistency
    console.log('\n🔄 Testing Cross-Strategy Consistency...')
    
    if (precompiledContent && filesystemContent) {
      const titleMatch = precompiledContent.title === filesystemContent.title
      const contentMatch = (precompiledContent.body as any).mdx === (filesystemContent.body as any).mdx
      
      console.log(`   ✅ Title consistency: ${titleMatch ? 'Match' : 'Mismatch'}`)
      console.log(`   ✅ Content consistency: ${contentMatch ? 'Match' : 'Mismatch'}`)
    }
    
    const listCountMatch = precompiledList.length === filesystemList.length
    const slugCountMatch = precompiledSlugs.length === filesystemSlugs.length
    
    console.log(`   ✅ List count consistency: ${listCountMatch ? 'Match' : 'Mismatch'}`)
    console.log(`   ✅ Slug count consistency: ${slugCountMatch ? 'Match' : 'Mismatch'}`)
    
    // Test 4: All Content Types
    console.log('\n📚 Testing All Content Types...')
    const contentTypes = ['blog', 'product', 'case-study'] as const
    
    for (const type of contentTypes) {
      const list = await precompiledProvider.getContentList(type, 'en')
      const slugs = await precompiledProvider.getAllContentSlugs(type)
      console.log(`   ✅ ${type}: ${list.length} items, ${slugs.length} total slugs`)
    }
    
    // Test 5: Platform Detection
    console.log('\n🔍 Testing Platform Detection...')
    const autoProvider = new MDXProvider()
    const autoInfo = autoProvider.getProviderInfo()
    console.log(`   ✅ Auto-detected strategy: ${autoInfo.strategy}`)
    
    // Test 6: Error Handling
    console.log('\n❌ Testing Error Handling...')
    const nonExistentContent = await precompiledProvider.getContent('blog', 'non-existent-post', 'en')
    console.log(`   ✅ Non-existent content: ${nonExistentContent === null ? 'Correctly returns null' : 'Error'}`)
    
    const invalidType = await precompiledProvider.getContentList('invalid-type' as any, 'en')
    console.log(`   ✅ Invalid content type: ${invalidType.length === 0 ? 'Correctly returns empty array' : 'Error'}`)
    
    console.log('\n🎉 All tests passed! MDX Provider is working correctly.')
    
    // Summary
    console.log('\n📊 Summary:')
    console.log(`   • Precompiled Strategy: ✅ Working`)
    console.log(`   • Filesystem Strategy: ✅ Working`)
    console.log(`   • Cross-Strategy Consistency: ✅ Verified`)
    console.log(`   • All Content Types: ✅ Supported`)
    console.log(`   • Platform Detection: ✅ Working`)
    console.log(`   • Error Handling: ✅ Robust`)
    
  } catch (error) {
    console.error('❌ Verification failed:', error)
    process.exit(1)
  }
}

// Run verification
if (require.main === module) {
  verifyMDXProvider()
}
