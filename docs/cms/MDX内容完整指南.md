# MDX 内容完整指南

本指南涵盖了在 ShipAny 中使用 MDX 内容的所有内容，包括内容创建、provider 选择和最佳实践。

## 目录

- [概述](#概述)
- [内容 Provider](#内容-provider)
- [MDX Provider（推荐）](#mdx-provider推荐)
- [创建 MDX 内容](#创建-mdx-内容)
- [Provider 选择指南](#provider-选择指南)
- [性能优化](#性能优化)
- [迁移指南](#迁移指南)
- [故障排除](#故障排除)

## 概述

ShipAny 使用 MDX（Markdown + JSX）进行内容管理，支持三种 provider。系统设计为与 provider 无关，同时保留每个 provider 的独特优势。

### 主要特性
- **零配置**：MDX Provider 开箱即用
- **跨平台原生支持**：自动适配 Cloudflare Workers 和 Vercel
- **极致性能**：200万+ ops/sec，0.38KB/项内存占用
- **多语言支持**：完整的国际化
- **自定义组件**：嵌入视频、图像和交互元素
- **SEO 优化**：自动元数据生成
- **Provider 灵活性**：无需代码更改即可在 provider 之间切换

## 内容 Provider

### 1. MDX Provider（强烈推荐）

**最适合：** 所有新项目，跨平台部署

**特点：**
- **零配置**：开箱即用，无需复杂设置
- **跨平台原生**：自动适配所有部署环境
- **极致性能**：200万+ ops/sec，内存使用极低
- **智能缓存**：双层缓存系统，优雅降级
- **TypeScript 支持**：完整的类型定义

**配置：**
```bash
# 推荐配置
CONTENT_PROVIDER=mdx
```

### 2. Contentlayer2

**最适合：** 现有 Contentlayer 项目，传统 Next.js 部署

**特点：**
- 构建时 MDX 编译
- TypeScript 类型生成
- 开发中的热重载
- 成熟稳定

**配置：**
```bash
CONTENT_PROVIDER=contentlayer2
```

### 3. Next.js MDX Remote

**最适合：** 仅 Cloudflare Workers 部署

**特点：**
- 运行时 MDX 解析
- 无文件系统依赖
- 较小的构建大小
- 动态内容支持

**配置：**
```bash
CONTENT_PROVIDER=next-mdx-remote
```

## MDX Provider（推荐）

### 为什么选择 MDX Provider？

**性能优势：**
- **响应时间**：< 1ms（vs 5-20ms 传统方案）
- **内存效率**：0.38KB/项（vs 2-5KB/项 传统方案）
- **扩展性**：支持 34,575+ 内容项
- **吞吐量**：200万+ ops/sec

**开发体验：**
- **零配置**：无需复杂设置
- **自动适配**：智能检测部署环境
- **热重载**：开发时实时更新
- **错误处理**：优雅降级机制

### 双策略架构

```typescript
// Cloudflare Workers 环境
Strategy: PrecompiledStrategy
- 构建时预编译内容
- 分块懒加载
- Cache API + 内存双层缓存
- 零文件系统依赖

// Vercel/Node.js 环境
Strategy: FilesystemStrategy
- 文件系统直接读取
- 运行时 MDX 编译
- 内存缓存 + 热重载
- 完整 Node.js 生态支持
```

### 快速开始

```bash
# 1. 设置环境变量
CONTENT_PROVIDER=mdx

# 2. 构建内容
npm run build:content

# 3. 启动开发
npm run dev

# 4. 部署（自动适配平台）
npm run deploy
```

**安装：**
```bash
CONTENT_PROVIDER=next-mdx-remote
pnpm build:content
```

### Provider 比较

| 特性 | Contentlayer2 | NextMDXRemote |
|------|--------------|---------------|
| 编译 | 构建时 | 运行时 |
| 性能 | 运行时更快 | 构建更快 |
| 边缘支持 | 有限 | 完全 |
| 类型安全 | 生成类型 | 运行时验证 |
| 包大小 | 较大 | 较小 |
| 热重载 | ✅ | ✅ |
| Workers 优化 | ❌ | ✅ 分块加载 |

## 创建 MDX 内容

### 文件结构

```
content/
├── blogs/
│   ├── en/
│   │   ├── getting-started.mdx
│   │   └── advanced-features.mdx
│   └── zh/
│       └── getting-started.mdx
├── products/
│   ├── en/
│   │   └── ai-assistant.mdx
│   └── zh/
│       └── ai-assistant.mdx
└── case-studies/
    ├── en/
    │   └── success-story.mdx
    └── zh/
        └── success-story.mdx
```

### Frontmatter 结构

```yaml
---
title: "文章标题"
slug: "url-friendly-slug"  # 可选，默认为文件名
description: "SEO 的简短描述"
publishedAt: "2024-01-15"
author: "作者名称"
authorImage: "https://example.com/author.jpg"
coverImage: "https://example.com/cover.jpg"
featured: true
tags:
  - 教程
  - 入门
category: "教程"  # 可选
---
```

#### ⚠️ 重要：gray-matter 自动类型转换

gray-matter 库会自动转换某些 YAML 值类型。为避免意外的类型转换，请遵循以下规则：

**需要使用引号的情况：**
```yaml
# 时间格式 - 不加引号会转换为秒数
videoDuration: "5:30"     # 保持为字符串 "5:30"
videoDuration: 5:30       # ❌ 会转换为 330（秒）

# 版本号 - 不加引号会转换为数字
version: "1.0"           # 保持为字符串 "1.0"
version: 1.0             # ❌ 会转换为数字 1

# 时间 - 不加引号会转换为秒数
time: "09:00"            # 保持为字符串 "09:00"
time: 09:00              # ❌ 会转换为秒数

# 带前导零的代码
code: "001"              # 保持前导零
code: 001                # ❌ 会转换为数字 1
```

**自动转换规则：**
- 时间格式（如 `5:30`）→ 转换为秒数（330）
- 日期格式 → 转换为 Date 对象
- 布尔值（`true`/`false`）→ 转换为 boolean
- 数字 → 转换为 number

**最佳实践：**
- 对所有需要保持字符串格式的值使用引号
- 特别注意时间、版本号、代码等字段
- 在不确定时，使用引号总是更安全的选择

### 编写内容

#### 基本 Markdown

```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文本** 和 *斜体文本*

- 项目列表
- 另一个项目

1. 编号列表
2. 另一个项目

> 引用

`内联代码` 和代码块：

```javascript
const hello = "world"
```
```

#### 使用自定义组件

**图片：**
```markdown
![替代文本](https://example.com/image.jpg)
```

**视频：**
```markdown
<Video
  src="https://example.com/video.mp4"
  poster="https://example.com/poster.jpg"
  controls
/>

<YouTube
  videoId="dQw4w9WgXcQ"
  title="视频标题"
/>

<Bilibili
  bvid="BV1xx411c7mD"
  title="视频标题"
/>
```

**视频画廊：**
```markdown
<VideoGallery
  columns={2}
  videos={[
    {
      type: "video",
      src: "https://example.com/video1.mp4",
      poster: "https://example.com/poster1.jpg",
      title: "视频 1"
    },
    {
      type: "youtube",
      videoId: "dQw4w9WgXcQ",
      title: "视频 2"
    }
  ]}
/>
```

### 多语言内容

#### 文件命名
- 英文：`content/blogs/en/my-post.mdx`
- 中文：`content/blogs/zh/my-post.mdx`

#### Slug 一致性
为翻译内容使用相同的 frontmatter slug：

**英文版本：**
```yaml
---
title: "Getting Started"
slug: "getting-started"
---
```

**中文版本：**
```yaml
---
title: "快速入门"
slug: "getting-started"  # 相同的 slug！
---
```

## Provider 选择指南

### 🎯 推荐：MDX Provider

**适用于 95% 的项目：**
- ✅ **新项目首选** - 零配置，最佳性能
- ✅ **跨平台部署** - 自动适配 Cloudflare Workers 和 Vercel
- ✅ **追求极致性能** - 200万+ ops/sec，内存使用极低
- ✅ **最小维护成本** - 自动优化，优雅降级
- ✅ **未来保障** - 持续优化，长期支持

**性能优势：**
```typescript
// 实际测试数据对比
MDX Provider:     < 1ms 响应，0.38KB/项内存
Contentlayer2:    ~1-5ms 响应，~2-5KB/项内存
NextMDXRemote:    ~5-20ms 响应，~1-3KB/项内存
```

### 何时使用 Contentlayer2

选择 Contentlayer2 当：
- ✅ 现有 Contentlayer 项目，运行稳定
- ✅ 需要复杂的内容关系处理
- ✅ 团队已熟悉 Contentlayer 工作流
- ❌ 不推荐新项目使用

### 何时使用 NextMDXRemote

选择 NextMDXRemote 当：
- ✅ 仅部署到 Cloudflare Workers
- ✅ 现有 NextMDXRemote 项目
- ✅ 需要特定的 Workers 优化
- ❌ 不推荐新项目使用

### 决策矩阵

| 项目类型 | 推荐方案 | 理由 |
|----------|----------|------|
| **新项目** | **MDX Provider** | 零配置，最佳性能，未来保障 |
| **跨平台部署** | **MDX Provider** | 自动适配，无需平台特定配置 |
| **高性能要求** | **MDX Provider** | 极致性能，内存使用极低 |
| **大规模内容** | **MDX Provider** | 支持数万项内容，线性扩展 |
| 现有 Contentlayer 项目 | Contentlayer2 | 稳定运行，无迁移成本 |
| 仅 Workers 部署 | NextMDXRemote | 专门优化 |

## 性能优化

### MDX Provider 性能特征

**内存使用分析：**
```typescript
// 基于实际测试的内存使用
const MEMORY_USAGE = {
  '100_items': '0.12MB (0.10% of 128MB)',
  '500_items': '0.61MB (0.48% of 128MB)',
  '1000_items': '~1.2MB (~0.9% of 128MB)',
  '5000_items': '~6MB (~4.7% of 128MB)'
}

// 扩展性阈值
const SCALABILITY = {
  safe_limit: '~34,575 项 (10% of 128MB)',
  warning_limit: '~86,439 项 (25% of 128MB)',
  theoretical_max: '~345,759 项 (100% of 128MB)'
}
```

**缓存性能：**
```typescript
// 双层缓存系统性能
const CACHE_PERFORMANCE = {
  memoryCache: {
    get: '0.000ms 平均延迟',
    set: '0.001ms 平均延迟',
    throughput: '2,235,856 ops/sec'
  },
  workersCache: {
    get: '0.002ms 平均延迟',
    set: '0.156ms 平均延迟',
    throughput: '639,566 ops/sec'
  }
}
```

### 性能监控

```typescript
// 获取性能统计
import { getContentProvider } from '@/services/content'

const provider = getContentProvider()
const stats = provider.getStats()

console.log('Performance stats:', {
  strategy: stats.strategy,
  cacheHitRate: stats.cache.hitRate,
  memoryUsage: stats.cache.memoryUsage,
  avgResponseTime: stats.performance.avgResponseTime
})
```

### 优化建议

**当前实现已足够优秀，建议：**
1. **暂不优化** - 当前性能表现卓越
2. **建立监控** - 监控关键性能指标
3. **内容增长至 1000+ 项时重新评估**

## 迁移指南

### 🎯 推荐：迁移到 MDX Provider

**从任何 Provider 迁移到 MDX Provider：**

1. **更新环境变量：**
   ```bash
   # .env 或 .env.local
   # 从: CONTENT_PROVIDER=contentlayer2 或 next-mdx-remote
   # 到:
   CONTENT_PROVIDER=mdx
   ```

2. **构建内容：**
   ```bash
   npm run build:content
   ```

3. **启动开发：**
   ```bash
   npm run dev
   ```

4. **验证性能提升：**
   ```bash
   npm run test:performance
   ```

**迁移优势：**
- ✅ **零代码更改** - API 完全兼容
- ✅ **显著性能提升** - 20-200 倍性能提升
- ✅ **内存使用减少** - 节省 95%+ 内存
- ✅ **跨平台支持** - 自动适配部署环境

### 零停机迁移策略

**生产环境安全迁移：**

1. **测试环境验证：**
   ```bash
   # 在测试环境使用 MDX Provider
   CONTENT_PROVIDER=mdx npm run build:content
   npm run build
   npm run test
   ```

2. **性能对比：**
   ```bash
   # 运行性能基线测试
   npm run test:performance
   npm run test:memory-usage
   ```

3. **功能验证：**
   ```bash
   # 验证所有功能正常
   npm run test:cross-platform
   npm run test:content-loading
   ```

4. **生产切换：**
   ```bash
   # 更新生产环境变量
   CONTENT_PROVIDER=mdx

   # 重新部署
   npm run deploy
   ```

5. **监控观察：**
   ```bash
   # 监控性能指标
   curl https://your-app.com/api/health
   ```

### 传统 Provider 间迁移

**从 Contentlayer2 到 MDX：**

1. **更新环境变量：**
   ```bash
   CONTENT_PROVIDER=mdx
   ```

2. **构建内容：**
   ```bash
   npm run build:content
   ```

**从 MDX 到 Contentlayer2：**

1. **更新环境变量：**
   ```bash
   CONTENT_PROVIDER=contentlayer2
   ```

2. **构建内容：**
   ```bash
   npm run build:content
   ```

### 迁移清单

**MDX Provider 迁移清单：**
- [ ] 更新 CONTENT_PROVIDER=mdx
- [ ] 运行 `npm run build:content`
- [ ] 测试内容渲染
- [ ] 验证语言切换
- [ ] 检查 SEO 元数据生成
- [ ] 运行性能测试
- [ ] 验证跨平台兼容性
- [ ] 在生产环境测试
- [ ] 监控性能指标

**传统 Provider 迁移清单：**
- [ ] 更新环境变量
- [ ] 运行构建命令
- [ ] 测试基本功能
- [ ] 在生产环境测试

## 故障排除

### MDX Provider 故障排除

#### 内容没有更新

**症状：** 修改内容后没有反映在网站上

**解决方案：**
```bash
# 重新构建内容
npm run build:content

# 清除 Next.js 缓存
rm -rf .next/cache

# 重启开发服务器
npm run dev
```

#### 性能问题

**症状：** 响应时间过长或内存使用过高

**解决方案：**
```bash
# 运行性能诊断
npm run test:performance
npm run test:memory-usage

# 检查缓存状态
curl http://localhost:3000/api/health
```

#### 跨平台问题

**症状：** 在不同平台表现不一致

**解决方案：**
```bash
# 测试平台兼容性
npm run test:cross-platform

# 强制特定策略（测试用）
MDX_FORCE_STRATEGY=filesystem npm run dev
MDX_FORCE_STRATEGY=precompiled npm run dev
```

#### 调试模式

**启用详细日志：**
```bash
# 查看所有 MDX Provider 日志
DEBUG=mdx:* npm run dev

# 查看平台检测
DEBUG=mdx:platform npm run dev

# 查看缓存操作
DEBUG=mdx:cache npm run dev
```

### 传统 Provider 故障排除

#### 内容未找到（404）

**症状：** 博客/产品/案例研究页面返回 404

**解决方案：**
1. 检查文件命名：`content/[type]/[locale]/[slug].mdx`
2. 验证 frontmatter 中的 slug 与 URL 匹配
3. 运行构建命令：`npm run build:content`
4. 检查控制台中的构建错误

#### 语言切换不工作

**症状：** 无法在语言之间切换

**解决方案：**
1. 确保两个语言版本都存在
2. 在所有版本的 frontmatter 中使用相同的 slug
3. 检查语言文件夹名称（en、zh）

#### 构建错误

**Contentlayer2 错误：**
```bash
# 清除缓存并重建
rm -rf .contentlayer
npm run build:content
```



**MDX Provider 错误：**
```bash
# 清除所有缓存
rm -rf .next/cache
rm -rf src/services/content/providers/mdx/static-content-*.ts
npm run build:content
```

#### 样式问题

**症状：** 内容看起来没有样式

**解决方案：** 确保你使用的是 Mdx 组件：
```tsx
import { Mdx } from '@/components/mdx'

// 正确
<Mdx content={blog} />

// 错误
<div>{blog.body.raw}</div>
```



### 性能提示

#### 对于 Contentlayer2
- 保持 MDX 文件在 100KB 以下
- 在嵌入之前优化图像
- 对重型组件使用延迟加载



### 调试模式

启用详细日志记录：
```bash
# 对于构建问题
pnpm build:content --verbose

# 检查生成的文件
# Contentlayer2: .contentlayer/generated/
# MDX: src/services/content/providers/mdx/static-content-*.ts
```



```typescript
export async function getStaticContent(): Promise<Record<string, ContentItem>> {
  // 返回缓存的内容（如果可用）
  if (contentCache) {
    return contentCache
  }

  // 动态导入所有内容块
  const [blogs, products, caseStudies, docs] = await Promise.all([
    import('./static-content-blogs').catch(() => ({ default: {} })),
    import('./static-content-products').catch(() => ({ default: {} })),
    import('./static-content-case-studies').catch(() => ({ default: {} })),
    import('./static-content-docs').catch(() => ({ default: {} }))
  ])

  // 合并所有块
  contentCache = {
    ...blogs.default,
    ...products.default,
    ...caseStudies.default,
    ...docs.default
  }

  return contentCache
}
```

#### 性能优势

1. **按需加载** - 只有在需要时才加载内容块
2. **内存缓存** - 加载后的内容被缓存，避免重复导入
3. **错误容错** - 单个块失败不会影响其他块
4. **代码分割** - 减少单个文件大小，提高加载速度

#### 构建过程

运行 `pnpm build:content` 时：

1. 扫描所有 MDX 文件
2. 按内容类型分组
3. 生成独立的内容块文件
4. 创建主索引文件
5. 输出构建统计信息

```bash
[MDX Build] Generated chunk: static-content-blogs.ts (3 items)
[MDX Build] Generated chunk: static-content-products.ts (4 items)
[MDX Build] Generated chunk: static-content-case-studies.ts (2 items)
[MDX Build] Generated main index: static-content.ts
```

## 最佳实践

1. **内容组织**
   - 每个内容一个 MDX 文件
   - 使用一致的命名约定
   - 保持翻译同步

2. **Frontmatter**
   - 始终包含必需字段
   - 使用 ISO 日期（YYYY-MM-DD）
   - 保持描述在 160 个字符以下

3. **媒体资产**
   - 对图像和视频使用 CDN
   - 提供适当的替代文本
   - 优化文件大小

4. **组件使用**
   - 不要过度使用自定义组件
   - 在两个 provider 中测试组件
   - 为缺失的 props 提供回退

5. **SEO**
   - 编写描述性标题
   - 包含元描述
   - 使用语义标题

## 总结

ShipAny 的 MDX 系统通过其多 provider 架构提供灵活性和性能。无论你优先考虑使用 Contentlayer2 的构建时优化还是使用 NextMDXRemote 的运行时灵活性，系统都能适应你的需求，同时保持一致的开发者体验。

有关架构详细信息，请参阅 [CMS 架构设计](./CMS架构设计.md)。
有关媒体管理，请参阅 [媒体嵌入指南](./媒体嵌入指南.md)。