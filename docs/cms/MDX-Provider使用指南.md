# MDX Provider 使用指南

MDX Provider 是 ShipAny 内容管理系统的新一代解决方案，提供零配置、跨平台原生支持和极致性能。

## 🚀 快速开始

### 1. 环境配置

```bash
# .env.local 或 .env
CONTENT_PROVIDER=mdx
```

### 2. 构建内容

```bash
# 构建内容（自动检测平台并优化）
npm run build:content

# 或使用 pnpm/yarn
pnpm build:content
yarn build:content
```

### 3. 启动开发

```bash
# 开发模式（自动热重载）
npm run dev

# 生产构建
npm run build

# 部署（自动适配平台）
npm run deploy
```

## 🏗️ 架构特性

### 双策略自动选择

MDX Provider 根据部署环境自动选择最优策略：

```typescript
// Cloudflare Workers 环境
Platform: Cloudflare Workers
Strategy: PrecompiledStrategy
Features: 
  - 预编译内容，分块加载
  - Cache API + 内存双层缓存
  - 零文件系统依赖
  - 极快冷启动

// Vercel/Node.js 环境  
Platform: Vercel/Node.js
Strategy: FilesystemStrategy
Features:
  - 文件系统直接读取
  - 运行时 MDX 编译
  - 内存缓存 + 热重载
  - 完整 Node.js 生态
```

### 智能缓存系统

```typescript
// 双层缓存架构
L1: MemoryCache     // 极速访问 (0.000ms)
L2: WorkersCache    // 持久化缓存 (Cache API)

// 性能指标
MemoryCache:  200万+ ops/sec
WorkersCache: 60万+ ops/sec
内存效率:     0.38KB/项
扩展性:       支持 34,575+ 项
```

## 📁 内容组织

### 推荐目录结构

```
content/
├── blogs/
│   ├── en/
│   │   ├── getting-started.mdx
│   │   └── advanced-tips.mdx
│   └── zh/
│       ├── getting-started.mdx
│       └── advanced-tips.mdx
├── products/
│   ├── en/
│   │   └── ai-content-generator.mdx
│   └── zh/
│       └── ai-content-generator.mdx
└── case-studies/
    ├── en/
    │   └── techcorp-transformation.mdx
    └── zh/
        └── techcorp-transformation.mdx
```

### MDX 文件格式

```mdx
---
title: "Getting Started with ShipAny"
description: "Learn how to get started with ShipAny platform"
publishedAt: "2024-01-15"
author: "ShipAny Team"
featured: true
tags: ["tutorial", "getting-started"]
coverImage: "/images/getting-started.jpg"
---

# Getting Started

Welcome to ShipAny! This guide will help you...

## Features

- ✅ Zero configuration
- ✅ Cross-platform native support
- ✅ Extreme performance

```typescript
// Code example
function example() {
  return "Hello ShipAny!"
}
```

## Next Steps

Continue with [Advanced Features](./advanced-features).
```

## ⚙️ 配置选项

### 环境变量

```bash
# 基础配置
CONTENT_PROVIDER=mdx                    # 启用 MDX Provider

# 可选配置（使用默认值）
MDX_CONTENT_DIR=./content               # 内容目录
MDX_CACHE_TTL=3600                      # 缓存 TTL（秒）
MDX_DEVELOPMENT_MODE=auto               # 开发模式

# 高级配置（通常不需要修改）
MDX_STATIC_CONTENT_DIR=src/services/content/providers/mdx/static-content
MDX_FILESYSTEM_INDEX_DIR=src/services/content/providers/mdx/filesystem-index

# 强制策略（仅用于测试）
MDX_FORCE_STRATEGY=filesystem           # 或 precompiled
```

### 构建配置

```typescript
// next.config.mjs 自动配置
// MDX Provider 会自动检测并应用最优配置
// 无需手动修改
```

## 🔧 API 使用

### 基础 API

```typescript
import { getContent, getContentList, getAllContentSlugs } from '@/services/content'

// 获取单个内容
const blog = await getContent('blog', 'getting-started', 'en')

// 获取内容列表
const blogs = await getContentList('blog', 'en')

// 获取所有 slugs
const slugs = await getAllContentSlugs('blog')

// 检查内容是否存在
const exists = await contentExists('blog', 'getting-started', 'en')

// 获取内容标题
const title = await getContentTitle('blog', 'getting-started', 'en')
```

### 高级 API

```typescript
import { getContentProvider } from '@/services/content'

// 获取 Provider 实例
const provider = getContentProvider()

// 获取统计信息
const stats = provider.getStats()
console.log('Provider stats:', stats)

// 获取平台信息
const platform = provider.getPlatformInfo()
console.log('Platform:', platform)
```

### ContentProvider 接口

MDX Provider 实现了完整的 ContentProvider 接口：

```typescript
class MDXProvider implements ContentProvider {
  // 获取单个内容项
  async getContent<T>(type: ContentType, slug: string, locale: string): Promise<T | null>

  // 获取内容列表
  async getContentList<T>(type: ContentType, locale: string, options?: QueryOptions): Promise<T[]>

  // 获取所有内容 slugs
  async getAllContentSlugs(type: ContentType): Promise<Array<{locale: string, slug: string}>>

  // 检查内容是否存在
  async contentExists(type: ContentType, slug: string, locale: string): Promise<boolean>

  // 获取内容标题
  async getContentTitle(type: ContentType, slug: string, locale: string): Promise<string | null>

  // 获取内容元数据
  async getContentMetadata(type: ContentType, slug: string, locale: string): Promise<ContentMetadata | null>
}
```

### 内容类型白名单

MDX Provider 只处理以下内容类型，其他目录（如 `docs`）会被忽略：

- `blogs` - 博客内容
- `products` - 产品内容
- `case-studies` - 案例研究内容

这确保了 MDX Provider 不会干扰其他系统（如文档系统）的内容。

## 🎨 组件使用

### MDX 渲染组件

```typescript
import { Mdx } from '@/components/mdx'

// 自动检测内容格式并渲染
export default function BlogPage({ content }) {
  return (
    <article>
      <h1>{content.title}</h1>
      <Mdx content={content} />
    </article>
  )
}
```

### 自定义组件

```typescript
// 在 MDX 中使用自定义组件
const components = {
  h1: ({ children }) => <h1 className="text-4xl font-bold">{children}</h1>,
  p: ({ children }) => <p className="text-gray-700">{children}</p>,
  // 自定义组件
  CalloutBox: ({ type, children }) => (
    <div className={`callout callout-${type}`}>
      {children}
    </div>
  )
}

<Mdx content={content} components={components} />
```

## 📊 性能监控

### 内置监控

```typescript
// 获取性能统计
const stats = provider.getStats()

// 示例输出
{
  strategy: 'precompiled',
  platform: 'cloudflare',
  cache: {
    hitRate: 0.95,
    totalEntries: 150,
    memoryUsage: '2.1MB'
  },
  performance: {
    avgResponseTime: 0.8,
    throughput: 1250000
  }
}
```

### 健康检查

```typescript
// API 路由: /api/health
export async function GET() {
  const provider = getContentProvider()
  
  try {
    // 测试基本功能
    const testContent = await provider.getContent('blog', 'test', 'en')
    const stats = provider.getStats()
    
    return Response.json({
      status: 'healthy',
      provider: 'mdx',
      stats,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return Response.json({
      status: 'unhealthy',
      error: error.message
    }, { status: 500 })
  }
}
```

## 🚀 部署指南

### Cloudflare Workers

```bash
# 自动检测并优化
npm run build:content
npm run build
wrangler deploy

# 验证部署
curl https://your-app.workers.dev/api/health
```

### Vercel

```bash
# 自动检测并优化
npm run build:content
npm run build
vercel deploy

# 验证部署
curl https://your-app.vercel.app/api/health
```

### Docker/VPS

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
ENV CONTENT_PROVIDER=mdx
RUN npm run build:content
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

## 🔍 故障排除

### 常见问题

**Q: 内容没有更新？**
```bash
# 重新构建内容
npm run build:content

# 清除缓存
rm -rf .next/cache
```

**Q: 性能问题？**
```bash
# 运行性能测试
npm run test:performance

# 检查内存使用
npm run test:memory-usage
```

**Q: 跨平台问题？**
```bash
# 测试平台兼容性
npm run test:cross-platform

# 强制特定策略（测试用）
MDX_FORCE_STRATEGY=filesystem npm run dev
```

### 调试模式

```bash
# 启用详细日志
DEBUG=mdx:* npm run dev

# 查看平台检测结果
DEBUG=mdx:platform npm run dev

# 查看缓存操作
DEBUG=mdx:cache npm run dev
```

## 📈 性能优化

### 当前性能表现

基于实际测试，MDX Provider 已经表现优异：

- **响应时间**: < 1ms
- **内存使用**: 0.38KB/项
- **扩展性**: 支持 34,575+ 项
- **缓存命中率**: 接近 100%

### 优化建议

**内容组织:**
```bash
# 按类型分组，便于分块加载
content/
├── blogs/     # 博客内容
├── products/  # 产品内容
└── docs/      # 文档内容
```

**缓存策略:**
```typescript
// 生产环境推荐配置
const CACHE_CONFIG = {
  ttl: 3600,           // 1 小时缓存
  cleanup: 1800,       // 30 分钟清理
  preload: true        // 启用预加载
}
```

## 🔄 迁移指南

### 从 Contentlayer2 迁移

```bash
# 1. 更新环境变量
# 从: CONTENT_PROVIDER=contentlayer2
# 到: CONTENT_PROVIDER=mdx

# 2. 构建内容
npm run build:content

# 3. 测试功能
npm run dev

# 4. 部署
npm run deploy
```



## 🧪 开发和测试

### 运行测试

```bash
# 运行验证脚本
npx tsx __tests__/verify-mdx-provider.ts

# 运行单元测试
npm test __tests__/mdx-provider.test.ts

# 运行跨平台兼容性测试
npm test __tests__/platform-compatibility.test.ts
```

### 性能测试

```bash
# 运行性能基准测试
npm test __tests__/performance.test.ts

# 内存使用分析
npm run test:memory-usage

# 跨平台兼容性测试
npm run test:cross-platform
```

## 📁 文件结构

```
src/services/content/providers/mdx/
├── provider.ts                    # 主 Provider 类
├── config.ts                     # 配置选项
├── types.ts                      # 类型定义
├── strategies/
│   ├── base-strategy.ts          # 策略基类
│   ├── precompiled-strategy.ts   # Cloudflare 策略
│   └── filesystem-strategy.ts    # Vercel 策略
├── loaders/
│   ├── static-loader.ts          # 静态内容加载器
│   └── dynamic-loader.ts         # 动态内容加载器
├── cache/
│   ├── memory-cache.ts           # 内存缓存
│   └── workers-cache.ts          # Workers 缓存
├── compilers/
│   └── mdx-compiler.ts           # MDX 编译器
└── utils/
    └── platform-detector.ts     # 平台检测
```

### 构建时生成的文件

```
src/services/content/providers/mdx/
├── static-content-blogs.ts        # 博客内容块
├── static-content-products.ts     # 产品内容块
├── static-content-case-studies.ts # 案例研究内容块
├── static-content-index.ts        # 内容索引
└── filesystem-index/
    ├── blogs.json                 # 博客索引
    ├── products.json              # 产品索引
    └── case-studies.json          # 案例研究索引
```

## 📚 相关文档

- [CMS 架构设计](./CMS架构设计.md) - 详细架构说明
- [MDX 内容完整指南](./MDX内容完整指南.md) - 内容创作指南
- [MDX-Provider缓存系统架构](./MDX-Provider缓存系统架构.md) - 缓存系统详解
