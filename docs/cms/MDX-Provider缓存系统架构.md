# 缓存系统架构

MDX Provider 实现了高性能的双层缓存系统，针对不同部署环境进行了优化，提供极致的性能表现。

## 概述

### 设计目标

- **极致性能**：毫秒级响应时间
- **内存效率**：最小化内存占用
- **跨平台兼容**：自动适配不同部署环境
- **优雅降级**：完善的错误处理和回退机制
- **零配置**：开箱即用，无需复杂设置

### 核心特性

- **双层缓存架构**：内存缓存 + 持久化缓存
- **智能平台检测**：自动选择最优缓存策略
- **自动降级机制**：API 不可用时的优雅回退
- **TTL 支持**：灵活的缓存过期策略
- **统计监控**：完整的性能指标收集

## 架构设计

### 缓存层级结构

```
┌─────────────────────────────────────────┐
│            应用层                        │
├─────────────────────────────────────────┤
│         缓存抽象层                       │
│  ┌─────────────────────────────────────┐ │
│  │        MemoryCache                  │ │ ← Vercel/Node.js
│  │     (高速内存缓存)                   │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │       WorkersCache                  │ │ ← Cloudflare Workers
│  │  ┌─────────────────────────────────┐ │ │
│  │  │      Memory Layer (L1)          │ │ │
│  │  └─────────────────────────────────┘ │ │
│  │  ┌─────────────────────────────────┐ │ │
│  │  │      Cache API (L2)             │ │ │
│  │  └─────────────────────────────────┘ │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 平台自适应

```typescript
// 自动平台检测和缓存选择
function createCache(config: CacheConfig): CacheInterface {
  const platform = detectPlatform()
  
  if (platform.platform === 'cloudflare') {
    // Cloudflare Workers: 双层缓存
    return new WorkersCache(config.ttl)
  } else {
    // Vercel/Node.js: 内存缓存
    return new MemoryCache(config.ttl)
  }
}
```

## MemoryCache（Vercel/Node.js 环境）

### 设计特点

- **纯内存存储**：极快的访问速度
- **TTL 支持**：自动过期清理
- **统计收集**：完整的性能指标
- **内存安全**：自动清理过期条目

### 实现细节

```typescript
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

class MemoryCache {
  private cache = new Map<string, CacheEntry>()
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0
  }
  
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.stats.misses++
      return null
    }
    
    // 检查是否过期
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.cache.delete(key)
      this.stats.misses++
      return null
    }
    
    this.stats.hits++
    return entry.data
  }
  
  set<T>(key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    }
    
    this.cache.set(key, entry)
    this.stats.sets++
  }
}
```

### 性能特征

- **GET 操作**：0.000ms 平均延迟
- **SET 操作**：0.001ms 平均延迟
- **吞吐量**：2,235,856 ops/sec
- **内存效率**：0.38KB/项

## WorkersCache（Cloudflare Workers 环境）

### 设计特点

- **双层架构**：内存层 + Cache API 层
- **智能回退**：API 不可用时自动降级
- **持久化存储**：跨请求共享缓存
- **边缘优化**：利用 Cloudflare 边缘网络

### 架构层次

#### L1 - 内存层

```typescript
class WorkersCache {
  private memoryCache = new Map<string, CacheEntry>()
  
  // 快速内存访问
  private getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key)
    if (!entry || this.isExpired(entry)) {
      return null
    }
    return entry.data
  }
  
  private setToMemory<T>(key: string, data: T, ttl: number): void {
    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
}
```

#### L2 - Cache API 层

```typescript
// 持久化缓存
async get<T>(key: string): Promise<T | null> {
  // 1. 首先检查内存缓存
  const memoryResult = this.getFromMemory<T>(key)
  if (memoryResult !== null) {
    return memoryResult
  }
  
  // 2. 检查 Cache API
  if (this.cacheAPI) {
    try {
      const cacheKey = this.getCacheKey(key)
      const response = await this.cacheAPI.match(cacheKey)
      
      if (response) {
        const entry = await response.json()
        
        // 检查是否过期
        if (!this.isExpired(entry)) {
          // 3. 回填内存缓存
          this.setToMemory(key, entry.data, entry.ttl)
          return entry.data
        }
      }
    } catch (error) {
      console.warn('Cache API error:', error)
    }
  }
  
  return null
}
```

### 降级机制

```typescript
async set<T>(key: string, data: T, ttl?: number): Promise<void> {
  const actualTTL = ttl || this.defaultTTL
  
  // 总是写入内存缓存
  this.setToMemory(key, data, actualTTL)
  
  // 尝试写入 Cache API
  if (this.cacheAPI) {
    try {
      const entry = {
        data,
        timestamp: Date.now(),
        ttl: actualTTL
      }
      
      const response = new Response(JSON.stringify(entry), {
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': `max-age=${actualTTL}`
        }
      })
      
      const cacheKey = this.getCacheKey(key)
      await this.cacheAPI.put(cacheKey, response)
    } catch (error) {
      console.warn('Cache API error, continuing with memory-only:', error)
    }
  }
}
```

### 性能特征

- **内存命中**：0.002ms 平均延迟
- **Cache API 命中**：5-15ms 平均延迟
- **SET 操作**：0.156ms 平均延迟
- **吞吐量**：639,566 ops/sec

## 缓存策略

### TTL 配置

```typescript
const CACHE_CONFIG = {
  // 开发环境：短 TTL，快速更新
  development: {
    ttl: 60,        // 1 分钟
    cleanup: 300    // 5 分钟清理一次
  },
  
  // 生产环境：长 TTL，性能优先
  production: {
    ttl: 3600,      // 1 小时
    cleanup: 1800   // 30 分钟清理一次
  }
}
```

### 缓存键策略

```typescript
// 生成缓存键
function generateCacheKey(type: string, slug: string, locale: string): string {
  return `content:${type}:${locale}:${slug}`
}

// WorkersCache 需要 URL 格式
function getCacheKey(key: string): string {
  const encodedKey = encodeURIComponent(key)
  return `https://mdx-cache.local/${encodedKey}`
}
```

### 自动清理

```typescript
// 定期清理过期条目
setInterval(() => {
  const removed = cache.cleanup()
  if (removed > 0) {
    console.log(`Cleaned up ${removed} expired cache entries`)
  }
}, CACHE_CONFIG.cleanup * 1000)
```

## 性能监控

### 统计信息

```typescript
interface CacheStats {
  totalEntries: number      // 总条目数
  validEntries: number      // 有效条目数
  expiredEntries: number    // 过期条目数
  hitRate: number          // 命中率
  memoryUsage?: number     // 内存使用量
  hasCacheAPI?: boolean    // Cache API 可用性
  
  // 操作统计
  operations: {
    hits: number
    misses: number
    sets: number
    deletes: number
  }
}
```

### 性能基线

```typescript
// 实际测试结果
const PERFORMANCE_BASELINE = {
  memoryCache: {
    get: { avg: 0.000, throughput: 2235856 },
    set: { avg: 0.001, throughput: 1839828 }
  },
  workersCache: {
    get: { avg: 0.002, throughput: 639566 },
    set: { avg: 0.156, throughput: 6406 }
  },
  memoryEfficiency: 0.38, // KB per item
  scalability: 34575      // Safe item count (10% of 128MB)
}
```

## 最佳实践

### 缓存配置

```typescript
// 推荐的生产环境配置
const PRODUCTION_CONFIG = {
  ttl: 3600,           // 1 小时缓存
  cleanup: 1800,       // 30 分钟清理
  maxEntries: 10000,   // 最大条目数
  memoryLimit: 50 * 1024 * 1024  // 50MB 内存限制
}
```

### 错误处理

```typescript
// 优雅的错误处理
try {
  const content = await cache.get(key)
  return content
} catch (error) {
  console.warn('Cache error, falling back to direct load:', error)
  return await loadContentDirectly(key)
}
```

### 监控集成

```typescript
// 健康检查端点
export async function GET() {
  const cache = getCache()
  const stats = cache.getStats()
  
  return Response.json({
    status: 'healthy',
    cache: {
      type: cache.constructor.name,
      stats,
      performance: {
        hitRate: stats.hitRate,
        memoryUsage: stats.memoryUsage
      }
    }
  })
}
```

## 扩展性分析

### 内存使用预测

```typescript
// 基于实际测试的扩展性数据
const SCALABILITY_METRICS = {
  current: {
    items: 9,
    memory: '< 0.01MB',
    status: '✅ 优秀'
  },
  projected: {
    100: { memory: '0.12MB', usage: '0.10%' },
    500: { memory: '0.61MB', usage: '0.48%' },
    1000: { memory: '1.2MB', usage: '0.9%' },
    5000: { memory: '6MB', usage: '4.7%' }
  },
  limits: {
    safe: '34,575 项 (10% of 128MB)',
    warning: '86,439 项 (25% of 128MB)',
    theoretical: '345,759 项 (100% of 128MB)'
  }
}
```

### 优化阈值

```typescript
const OPTIMIZATION_THRESHOLDS = {
  memory_usage: 10 * 1024 * 1024,    // 10MB
  response_time: 50,                  // 50ms
  cache_hit_rate: 0.8,               // 80%
  content_count: 1000                 // 1000 项
}
```

## 总结

MDX Provider 的缓存系统通过以下特性实现了卓越的性能：

1. **智能平台适配**：自动选择最优缓存策略
2. **双层架构**：结合内存速度和持久化优势
3. **优雅降级**：确保在任何情况下都能正常工作
4. **极致性能**：毫秒级响应，极低内存占用
5. **零配置**：开箱即用，无需复杂设置

这使得 MDX Provider 能够在各种部署环境中提供一致的高性能体验。
