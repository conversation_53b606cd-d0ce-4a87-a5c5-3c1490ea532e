/**
 * Provider Selector - Build-time Content Provider Selection
 * 
 * This module implements build-time provider selection to optimize bundle size
 * and performance. The provider is determined at build time based on the
 * CONTENT_PROVIDER environment variable.
 * 
 * Key Features:
 * - Build-time provider selection (no runtime overhead)
 * - Tree-shaking friendly (unused providers are eliminated)
 * - Type-safe provider imports
 * - Clear error messages for configuration issues
 * 
 * Provider Selection Guide:
 * 
 * 1. Contentlayer2 (default):
 *    - Best for: Traditional Next.js deployments (Vercel, VPS)
 *    - Pros: Build-time compilation, excellent DX, type generation
 *    - Cons: Requires Node.js build environment, larger build artifacts
 *    - Use when: You have a standard Node.js build pipeline
 * 
 * 2. MDX:
 *    - Best for: Cross-platform deployments (both Cloudflare Workers and Vercel)
 *    - Pros: Platform-adaptive, zero configuration, Next.js native
 *    - Cons: Newer implementation, less battle-tested
 *    - Use when: Need maximum deployment flexibility
 *
 * Configuration:
 * Set CONTENT_PROVIDER environment variable to:
 * - 'contentlayer2' (default)
 * - 'mdx'
 */

import type { ContentProvider } from '../types'
import type { ProviderConfig } from '../types'

// Provider selection happens at build time
const PROVIDER = process.env.CONTENT_PROVIDER || 'contentlayer2'

/**
 * Get the content provider instance
 * 
 * This function uses dynamic imports with static strings to ensure
 * webpack can tree-shake unused providers at build time.
 * 
 * @param config - Optional provider-specific configuration
 * @returns Promise resolving to the provider instance
 */
export async function getContentProvider(
  config?: ProviderConfig
): Promise<ContentProvider> {
  console.log(`[Provider Selector] Loading provider: ${PROVIDER}`)
  
  try {
    switch (PROVIDER) {
      case 'contentlayer2': {
        // Dynamic import with static path for tree-shaking
        const { Contentlayer2Provider } = await import('../providers/contentlayer2')
        return new Contentlayer2Provider()
      }

      case 'mdx': {
        // Dynamic import with static path for tree-shaking
        const { MDXProvider } = await import('../providers/mdx')
        return new MDXProvider(config?.mdx)
      }

      default:
        throw new Error(
          `Unknown content provider: ${PROVIDER}. ` +
          `Valid options are: 'contentlayer2', 'mdx'`
        )
    }
  } catch (error) {
    console.error(`[Provider Selector] Failed to load provider ${PROVIDER}:`, error)
    throw new Error(
      `Failed to load content provider '${PROVIDER}'. ` +
      `Make sure the provider is properly installed and configured.`
    )
  }
}

/**
 * Get the current provider name
 * 
 * Useful for conditional logic based on the active provider
 * 
 * @returns The provider name
 */
export function getProviderName(): string {
  return PROVIDER
}

/**
 * Check if a specific provider is active
 * 
 * @param providerName - The provider name to check
 * @returns True if the provider is active
 */
export function isProvider(providerName: string): boolean {
  return PROVIDER === providerName
}

/**
 * Provider feature detection
 * 
 * Different providers support different features. This function
 * provides a way to check feature availability at build time.
 */
export const providerFeatures = {
  // Contentlayer2 features
  typeGeneration: PROVIDER === 'contentlayer2',
  buildTimeCompilation: PROVIDER === 'contentlayer2',
  hotReload: PROVIDER === 'contentlayer2',

  // MDX features
  edgeCompatible: PROVIDER === 'mdx',
  dynamicContent: PROVIDER === 'mdx',
  runtimeParsing: PROVIDER === 'mdx',
  platformAdaptive: PROVIDER === 'mdx',
  crossPlatformCompatible: PROVIDER === 'mdx',
  zeroConfiguration: PROVIDER === 'mdx',

  // Common features
  mdxSupport: true,
  i18nSupport: true,
  seoMetadata: true,
} as const

/**
 * Export provider info for client-side usage
 * 
 * This is mainly for debugging and conditional UI rendering
 */
export const providerInfo = {
  name: PROVIDER,
  features: providerFeatures,
} as const