/**
 * Type definitions for MDX Content Provider
 * 
 * This module defines the configuration and data structures
 * specific to the MDX content provider implementation that supports
 * both Cloudflare Workers and Vercel deployments.
 */

import type { ContentItem, ContentType, QueryOptions, ContentMetadata } from '../../types'

/**
 * Configuration options for MDX Provider
 */
export interface MDXProviderConfig {
  /**
   * Directory containing MDX source files
   * @default './content'
   */
  contentDir?: string
  
  /**
   * Directory for generated static content files
   * @default 'src/services/content/providers/mdx/static-content'
   */
  staticContentDir?: string
  
  /**
   * Directory for filesystem index files
   * @default 'src/services/content/providers/mdx/filesystem-index'
   */
  filesystemIndexDir?: string
  
  /**
   * Cache directory for compiled content
   * @default '.mdx-cache'
   */
  cacheDir?: string
  
  /**
   * Cache TTL in seconds
   * @default 3600
   */
  cacheTTL?: number
  
  /**
   * Enable development mode features
   * @default process.env.NODE_ENV === 'development'
   */
  developmentMode?: boolean
  
  /**
   * Force specific strategy (for testing)
   * @default undefined (auto-detect)
   */
  forceStrategy?: 'precompiled' | 'filesystem'
}

/**
 * Default configuration values
 */
export const DEFAULT_CONFIG: Omit<Required<MDXProviderConfig>, 'forceStrategy'> & { forceStrategy?: 'precompiled' | 'filesystem' } = {
  contentDir: './content',
  staticContentDir: 'src/services/content/providers/mdx/static-content',
  filesystemIndexDir: 'src/services/content/providers/mdx/filesystem-index',
  cacheDir: '.mdx-cache',
  cacheTTL: 3600,
  developmentMode: process.env.NODE_ENV === 'development',
  forceStrategy: undefined
}

/**
 * MDX Content structure with platform-specific body formats
 */
export interface MDXContentItem {
  // Core identification fields
  slug: string
  title: string
  lang: string
  url: string

  // Content type (discriminated union)
  type: ContentType

  // Content and metadata
  description?: string
  body: {
    /** Raw MDX source content */
    mdx: string
    /** Compiled React component (for precompiled strategy) */
    component?: React.ComponentType<any>
    /** HTML output (fallback) */
    html?: string
  }

  // Media and visual content
  coverImage?: string
  authorImage?: string
  videoUrl?: string
  videoThumbnail?: string
  videoDuration?: string

  // Publishing information
  author?: string
  publishedAt?: string
  createdAt: string
  featured: boolean
  tags?: string[]

  // Type-specific properties
  icon?: string // For products

  // Additional properties from frontmatter
  [key: string]: any
}

/**
 * Content chunk structure for static loading
 */
export interface ContentChunk {
  [key: string]: MDXContentItem
}

/**
 * File mapping entry for filesystem strategy
 */
export interface FileMappingEntry {
  slug: string
  file: string
  lastModified: string
  checksum: string
}

/**
 * Content index structure for filesystem strategy
 */
export interface ContentIndex {
  [contentType: string]: {
    [locale: string]: FileMappingEntry[]
  }
}

/**
 * Build manifest structure
 */
export interface BuildManifest {
  timestamp: string
  target: string
  cloudflareAssets: boolean
  vercelAssets: boolean
  contentCount: number
  chunkCount: number
}

/**
 * Cache entry structure
 */
export interface CacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
}

/**
 * Strategy interface for platform-specific implementations
 */
export interface StrategyInterface {
  readonly name: string

  getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null>

  getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]>

  getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]>

  getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>>

  contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean>

  getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null>

  getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null>
}
