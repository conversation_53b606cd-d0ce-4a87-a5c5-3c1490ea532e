/**
 * Platform Detection Utilities for MDX Provider
 * 
 * This module provides utilities to detect the deployment platform
 * and select the appropriate strategy for content loading.
 */

import { targetPlatform, isCloudflareEnvironment } from '@/lib/platform'
import type { MDXProviderConfig } from '../types'

/**
 * Strategy types for different platforms
 */
export type StrategyType = 'precompiled' | 'filesystem'

/**
 * Detect the appropriate strategy based on platform and configuration
 * 
 * @param config - MDX provider configuration
 * @returns The strategy type to use
 */
export function detectStrategy(config: Required<MDXProviderConfig>): StrategyType {
  // If strategy is forced in config, use it
  if (config.forceStrategy) {
    console.log(`[Platform Detector] Using forced strategy: ${config.forceStrategy}`)
    return config.forceStrategy
  }
  
  // Use platform detection from @/lib/platform
  const strategy = isCloudflareEnvironment() ? 'precompiled' : 'filesystem'
  
  console.log(`[Platform Detector] Detected platform: ${targetPlatform}, using strategy: ${strategy}`)
  
  return strategy
}

/**
 * Check if static content files are available
 * 
 * @returns True if static content files exist
 */
export function hasStaticContent(): boolean {
  try {
    // Try to resolve the static content module
    require.resolve('../static-content/static-content')
    return true
  } catch {
    return false
  }
}

/**
 * Check if filesystem index is available
 * 
 * @returns True if filesystem index exists
 */
export function hasFilesystemIndex(): boolean {
  try {
    // Try to resolve the filesystem index module
    require.resolve('../filesystem-index/content-index')
    return true
  } catch {
    return false
  }
}

/**
 * Get platform-specific capabilities
 * 
 * @returns Object describing platform capabilities
 */
export function getPlatformCapabilities() {
  return {
    platform: targetPlatform,
    hasFileSystem: !isCloudflareEnvironment(),
    hasStaticContent: hasStaticContent(),
    hasFilesystemIndex: hasFilesystemIndex(),
    supportsNodeAPIs: !isCloudflareEnvironment(),
    memoryLimited: isCloudflareEnvironment(),
    recommendedStrategy: detectStrategy({ forceStrategy: undefined } as any)
  }
}

/**
 * Log platform detection information
 */
export function logPlatformInfo(): void {
  const capabilities = getPlatformCapabilities()
  
  console.log('[Platform Detector] Platform capabilities:', {
    platform: capabilities.platform,
    strategy: capabilities.recommendedStrategy,
    hasFileSystem: capabilities.hasFileSystem,
    hasStaticContent: capabilities.hasStaticContent,
    hasFilesystemIndex: capabilities.hasFilesystemIndex
  })
}
