/**
 * Base Strategy for MDX Content Provider
 * 
 * This abstract class defines the interface that all platform-specific
 * strategies must implement. It provides common functionality and
 * ensures consistent behavior across different deployment environments.
 */

import type { 
  ContentItem, 
  ContentType, 
  QueryOptions, 
  ContentMetadata 
} from '../../../types'
import type { MDXProviderConfig, StrategyInterface } from '../types'

/**
 * Abstract base class for content loading strategies
 */
export abstract class BaseStrategy implements StrategyInterface {
  abstract readonly name: string
  
  protected config: Required<MDXProviderConfig>
  
  constructor(config: Required<MDXProviderConfig>) {
    this.config = config
  }
  
  /**
   * Get a single content item by type, slug, and locale
   */
  abstract getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null>
  
  /**
   * Get a list of content items with optional filtering and sorting
   */
  abstract getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]>
  
  /**
   * Get all content for static generation
   */
  abstract getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]>
  
  /**
   * Get all content slugs for static path generation
   */
  abstract getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>>
  
  /**
   * Check if content exists
   */
  abstract contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean>
  
  /**
   * Get content title only
   */
  abstract getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null>
  
  /**
   * Get content metadata
   */
  abstract getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null>
  
  /**
   * Normalize content type for internal use
   * 
   * @param type - Content type
   * @returns Normalized content type
   */
  protected normalizeContentType(type: ContentType): string {
    // Convert case-study to case-studies for consistency
    return type === 'case-study' ? 'case-studies' : `${type}s`
  }
  
  /**
   * Generate content key for caching
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Content locale
   * @returns Cache key
   */
  protected generateContentKey(type: ContentType, slug: string, locale: string): string {
    return `${type}-${slug}-${locale}`
  }
  
  /**
   * Filter and sort content list based on options
   * 
   * @param items - Content items to filter
   * @param options - Query options
   * @returns Filtered and sorted items
   */
  protected filterAndSortContent<T extends ContentItem>(
    items: T[],
    options?: QueryOptions
  ): T[] {
    let filtered = [...items]
    
    // Apply filters
    if (options?.featured !== undefined) {
      filtered = filtered.filter(item => item.featured === options.featured)
    }
    
    if (options?.tags && options.tags.length > 0) {
      filtered = filtered.filter(item => 
        options.tags!.some(tag => item.tags?.includes(tag))
      )
    }
    
    // Apply sorting
    if (options?.sortBy) {
      filtered.sort((a, b) => {
        const aValue = a[options.sortBy as keyof T] as any
        const bValue = b[options.sortBy as keyof T] as any
        
        if (options.order === 'desc') {
          return bValue > aValue ? 1 : -1
        } else {
          return aValue > bValue ? 1 : -1
        }
      })
    }
    
    // Apply pagination
    if (options?.limit) {
      const start = (options.offset || 0)
      filtered = filtered.slice(start, start + options.limit)
    }
    
    return filtered
  }
  
  /**
   * Validate content type
   * 
   * @param type - Content type to validate
   * @returns True if valid
   */
  protected isValidContentType(type: string): type is ContentType {
    return ['blog', 'product', 'case-study'].includes(type)
  }
  
  /**
   * Validate locale
   * 
   * @param locale - Locale to validate
   * @returns True if valid
   */
  protected isValidLocale(locale: string): boolean {
    return ['en', 'zh'].includes(locale)
  }
}
