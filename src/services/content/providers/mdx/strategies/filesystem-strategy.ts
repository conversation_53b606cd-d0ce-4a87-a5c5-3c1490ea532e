/**
 * Filesystem Strategy for Vercel/Node.js
 * 
 * This strategy loads content dynamically from the filesystem,
 * optimized for Node.js environments with full filesystem access.
 */

import { BaseStrategy } from './base-strategy'
import { MemoryCache } from '../cache/memory-cache'
import { MDXCompiler } from '../compilers/mdx-compiler'
import type {
  ContentItem,
  ContentType,
  QueryOptions,
  ContentMetadata
} from '../../../types'
import type { MDXProviderConfig, ContentIndex, FileMappingEntry } from '../types'

/**
 * Filesystem-based content loading strategy for Node.js environments
 */
export class FilesystemStrategy extends BaseStrategy {
  readonly name = 'filesystem'
  
  private cache: MemoryCache
  private compiler: MDXCompiler
  private contentIndex: ContentIndex | null = null
  private indexLoaded = false

  constructor(config: Required<MDXProviderConfig>) {
    super(config)
    this.cache = new MemoryCache(config.cacheTTL)
    this.compiler = new MDXCompiler(config.contentDir)

    console.log('[FilesystemStrategy] Initialized for Node.js environment')
  }
  
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    if (!this.isValidContentType(type) || !this.isValidLocale(locale)) {
      return null
    }
    
    const contentKey = this.generateContentKey(type, slug, locale)
    
    // Check cache first
    const cached = this.cache.get<T>(contentKey)
    if (cached) {
      return cached
    }
    
    // Load from filesystem
    const content = await this.loadContentFromFilesystem<T>(type, slug, locale)
    
    // Cache the result
    if (content) {
      this.cache.set(contentKey, content)
    }
    
    return content
  }
  
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]> {
    if (!this.isValidContentType(type) || !this.isValidLocale(locale)) {
      return []
    }
    
    const cacheKey = `list-${type}-${locale}-${JSON.stringify(options || {})}`
    
    // Check cache first
    const cached = this.cache.get<T[]>(cacheKey)
    if (cached) {
      return cached
    }
    
    // Load content index
    await this.ensureIndexLoaded()
    
    const items: T[] = []
    const typeKey = this.normalizeContentType(type)

    if (this.contentIndex?.[typeKey]?.[locale]) {
      const entries = this.contentIndex[typeKey][locale]

      // Load all content items
      for (const entry of entries) {
        const content = await this.loadContentFromFilesystem<T>(type, entry.slug, locale)
        if (content) {
          items.push(content)
        }
      }
    } else {
      // Fallback to directory scanning if no index
      const directItems = await this.compiler.scanDirectory(type, locale)
      items.push(...(directItems as unknown as T[]))
    }
    
    // Apply filtering and sorting
    const filtered = this.filterAndSortContent(items, options)
    
    // Cache the result
    this.cache.set(cacheKey, filtered, 1800) // 30 minutes for lists
    
    return filtered
  }
  
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    if (!this.isValidContentType(type)) {
      return []
    }
    
    await this.ensureIndexLoaded()
    
    const items: T[] = []
    const typeKey = this.normalizeContentType(type)

    if (this.contentIndex?.[typeKey]) {
      for (const [locale, entries] of Object.entries(this.contentIndex[typeKey])) {
        for (const entry of entries) {
          const content = await this.loadContentFromFilesystem<T>(type, entry.slug, locale)
          if (content) {
            items.push(content)
          }
        }
      }
    } else {
      // Fallback to scanning all locales
      const locales = ['en', 'zh'] // Could be made configurable
      for (const locale of locales) {
        const directItems = await this.compiler.scanDirectory(type, locale)
        items.push(...(directItems as unknown as T[]))
      }
    }

    return items
  }
  
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    if (!this.isValidContentType(type)) {
      return []
    }
    
    await this.ensureIndexLoaded()
    
    const slugs: Array<{ locale: string; slug: string }> = []
    const typeKey = this.normalizeContentType(type)

    if (this.contentIndex?.[typeKey]) {
      for (const [locale, entries] of Object.entries(this.contentIndex[typeKey])) {
        for (const entry of entries) {
          slugs.push({
            locale,
            slug: entry.slug
          })
        }
      }
    } else {
      // Fallback to filesystem scanning
      const directSlugs = await this.compiler.getAllSlugs(type)
      slugs.push(...directSlugs)
    }

    return slugs
  }
  
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    const content = await this.getContent(type, slug, locale)
    return content !== null
  }
  
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null> {
    const content = await this.getContent(type, slug, locale)
    return content?.title || null
  }
  
  async getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null> {
    const content = await this.getContent(type, slug, locale)
    
    if (!content) {
      return null
    }
    
    // Calculate word count and reading time
    const mdxContent = (content.body as any).mdx || ''
    const wordCount = mdxContent.split(/\s+/).filter((word: string) => word.length > 0).length
    const readingTime = Math.ceil(wordCount / 200) // Assuming 200 words per minute

    return {
      wordCount,
      readingTime,
      publishedAt: content.publishedAt,
      updatedAt: (content as any).updatedAt,
      author: content.author,
      tags: content.tags || []
    }
  }
  
  /**
   * Load content from filesystem using index or direct file access
   */
  async loadContentFromFilesystem<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    try {
      // Try to use filesystem index first
      await this.ensureIndexLoaded()
      
      if (this.contentIndex) {
        return this.loadFromIndex<T>(type, slug, locale)
      } else {
        // Fallback to direct filesystem access
        return this.loadFromDirectAccess<T>(type, slug, locale)
      }
    } catch (error) {
      console.warn(`[FilesystemStrategy] Failed to load content ${type}/${slug}/${locale}:`, error)
      return null
    }
  }
  
  /**
   * Load content using the filesystem index
   */
  private async loadFromIndex<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    const typeKey = this.normalizeContentType(type)
    const entries = this.contentIndex?.[typeKey]?.[locale]

    if (!entries) {
      return null
    }

    const entry = entries.find(e => e.slug === slug)
    if (!entry) {
      return null
    }

    // Load and compile the MDX file using the compiler
    return this.compiler.loadFromIndex(type, slug, locale, entry.file) as Promise<T | null>
  }
  
  /**
   * Load content via direct filesystem access
   */
  private async loadFromDirectAccess<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    // Use the compiler to load directly from filesystem
    return this.compiler.loadDirect(type, slug, locale) as Promise<T | null>
  }
  

  
  /**
   * Ensure content index is loaded
   */
  private async ensureIndexLoaded(): Promise<void> {
    if (this.indexLoaded) {
      return
    }
    
    try {
      // Try to load the generated index
      const { contentIndex } = await import('../filesystem-index/content-index')
      this.contentIndex = contentIndex
      console.log('[FilesystemStrategy] Loaded content index')
    } catch (error) {
      console.warn('[FilesystemStrategy] No content index found, will use direct access')
      this.contentIndex = null
    }
    
    this.indexLoaded = true
  }
}
