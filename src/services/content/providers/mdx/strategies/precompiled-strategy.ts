/**
 * Precompiled Strategy for Cloudflare Workers
 * 
 * This strategy loads pre-compiled static content chunks optimized for
 * Cloudflare Workers environment with memory constraints.
 */

import { BaseStrategy } from './base-strategy'
import { WorkersCache } from '../cache/workers-cache'
import type { 
  ContentItem, 
  ContentType, 
  QueryOptions, 
  ContentMetadata 
} from '../../../types'
import type { MDXProviderConfig, ContentChunk } from '../types'

/**
 * Precompiled content loading strategy for Cloudflare Workers
 */
export class PrecompiledStrategy extends BaseStrategy {
  readonly name = 'precompiled'
  
  private cache: WorkersCache
  private contentChunks = new Map<string, Promise<ContentChunk>>()
  private loadedChunks = new Set<string>()
  
  constructor(config: Required<MDXProviderConfig>) {
    super(config)
    this.cache = new WorkersCache(config.cacheTTL)
    
    console.log('[PrecompiledStrategy] Initialized for Cloudflare Workers environment')
  }
  
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    if (!this.isValidContentType(type) || !this.isValidLocale(locale)) {
      return null
    }
    
    const contentKey = this.generateContentKey(type, slug, locale)
    
    // Check cache first
    const cached = await this.cache.get<T>(contentKey)
    if (cached) {
      return cached
    }
    
    // Load from static chunks
    const content = await this.loadContentFromChunks<T>(type, slug, locale)
    
    // Cache the result
    if (content) {
      await this.cache.set(contentKey, content)
    }
    
    return content
  }
  
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]> {
    if (!this.isValidContentType(type) || !this.isValidLocale(locale)) {
      return []
    }
    
    const cacheKey = `list-${type}-${locale}-${JSON.stringify(options || {})}`
    
    // Check cache first
    const cached = await this.cache.get<T[]>(cacheKey)
    if (cached) {
      return cached
    }
    
    // Load all content of this type
    const chunk = await this.loadChunk(this.getChunkKey(type))
    const items: T[] = []
    
    for (const [key, item] of Object.entries(chunk)) {
      if (key.startsWith(`${type}-`) && key.endsWith(`-${locale}`)) {
        items.push(item as unknown as T)
      }
    }
    
    // Apply filtering and sorting
    const filtered = this.filterAndSortContent(items, options)
    
    // Cache the result
    await this.cache.set(cacheKey, filtered, 1800) // 30 minutes for lists
    
    return filtered
  }
  
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    if (!this.isValidContentType(type)) {
      return []
    }
    
    const chunk = await this.loadChunk(this.getChunkKey(type))
    const items: T[] = []
    
    for (const [key, item] of Object.entries(chunk)) {
      if (key.startsWith(`${type}-`)) {
        items.push(item as unknown as T)
      }
    }
    
    return items
  }
  
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    if (!this.isValidContentType(type)) {
      return []
    }
    
    const chunk = await this.loadChunk(this.getChunkKey(type))
    const slugs: Array<{ locale: string; slug: string }> = []
    
    for (const [key, item] of Object.entries(chunk)) {
      if (key.startsWith(`${type}-`)) {
        slugs.push({
          locale: item.lang,
          slug: item.slug
        })
      }
    }
    
    return slugs
  }
  
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    const content = await this.getContent(type, slug, locale)
    return content !== null
  }
  
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null> {
    const content = await this.getContent(type, slug, locale)
    return content?.title || null
  }
  
  async getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null> {
    const content = await this.getContent(type, slug, locale)
    
    if (!content) {
      return null
    }
    
    // Calculate word count and reading time
    const mdxContent = (content.body as any).mdx || ''
    const wordCount = mdxContent.split(/\s+/).filter((word: string) => word.length > 0).length
    const readingTime = Math.ceil(wordCount / 200) // Assuming 200 words per minute

    return {
      wordCount,
      readingTime,
      publishedAt: content.publishedAt,
      updatedAt: (content as any).updatedAt,
      author: content.author,
      tags: content.tags || []
    }
  }
  
  /**
   * Load content from static chunks
   */
  private async loadContentFromChunks<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    const chunkKey = this.getChunkKey(type)
    const chunk = await this.loadChunk(chunkKey)
    const contentKey = this.generateContentKey(type, slug, locale)
    
    return (chunk[contentKey] as unknown as T) || null
  }
  
  /**
   * Load a specific content chunk
   */
  private async loadChunk(chunkKey: string): Promise<ContentChunk> {
    // Return cached promise if already loading
    if (this.contentChunks.has(chunkKey)) {
      return this.contentChunks.get(chunkKey)!
    }
    
    // Create loading promise
    const loadingPromise = this.loadChunkFromStatic(chunkKey)
    this.contentChunks.set(chunkKey, loadingPromise)
    
    try {
      const chunk = await loadingPromise
      this.loadedChunks.add(chunkKey)
      return chunk
    } catch (error) {
      // Remove failed promise so it can be retried
      this.contentChunks.delete(chunkKey)
      throw error
    }
  }
  
  /**
   * Load chunk from static imports
   */
  private async loadChunkFromStatic(chunkKey: string): Promise<ContentChunk> {
    try {
      console.log(`[PrecompiledStrategy] Loading chunk: ${chunkKey}`)
      
      // Dynamic import with static string for tree-shaking
      switch (chunkKey) {
        case 'blogs':
          return (await import('../static-content/static-content-blogs')).default
        case 'products':
          return (await import('../static-content/static-content-products')).default
        case 'case-studies':
          return (await import('../static-content/static-content-case-studies')).default
        default:
          console.warn(`[PrecompiledStrategy] Unknown chunk: ${chunkKey}`)
          return {}
      }
    } catch (error) {
      console.warn(`[PrecompiledStrategy] Failed to load chunk ${chunkKey}:`, error)
      return {}
    }
  }
  
  /**
   * Get chunk key for content type
   */
  private getChunkKey(type: ContentType): string {
    return this.normalizeContentType(type)
  }
}
