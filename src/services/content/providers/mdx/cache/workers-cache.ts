/**
 * Workers Cache Implementation for MDX Provider
 * 
 * This module provides a caching solution optimized for Cloudflare Workers
 * using the Cache API and in-memory storage.
 */

import type { CacheEntry } from '../types'

/**
 * Cloudflare Workers cache implementation
 */
export class WorkersCache {
  private memoryCache = new Map<string, CacheEntry>()
  private cacheAPI?: Cache
  private defaultTTL: number
  
  constructor(defaultTTL: number = 3600) {
    this.defaultTTL = defaultTTL
    
    // Initialize Cache API if available
    if (typeof caches !== 'undefined') {
      this.cacheAPI = (caches as any).default || caches
    }
  }
  
  /**
   * Get item from cache
   * 
   * @param key - Cache key
   * @returns Cached data or null if not found/expired
   */
  async get<T>(key: string): Promise<T | null> {
    // First check memory cache
    const memoryResult = this.getFromMemory<T>(key)
    if (memoryResult !== null) {
      return memoryResult
    }
    
    // Then check Cache API if available
    if (this.cacheAPI) {
      try {
        const response = await this.cacheAPI.match(this.getCacheKey(key))
        if (response) {
          const entry: CacheEntry<T> = await response.json()
          
          // Check if entry has expired
          const now = Date.now()
          if (now <= entry.timestamp + entry.ttl * 1000) {
            // Store in memory cache for faster subsequent access
            this.memoryCache.set(key, entry)
            return entry.data
          } else {
            // Remove expired entry
            await this.cacheAPI.delete(this.getCacheKey(key))
          }
        }
      } catch (error) {
        console.warn('[WorkersCache] Cache API error:', error)
      }
    }
    
    return null
  }
  
  /**
   * Set item in cache
   * 
   * @param key - Cache key
   * @param data - Data to cache
   * @param ttl - Time to live in seconds (optional)
   */
  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    }
    
    // Store in memory cache
    this.memoryCache.set(key, entry)
    
    // Store in Cache API if available
    if (this.cacheAPI) {
      try {
        const response = new Response(JSON.stringify(entry), {
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': `max-age=${entry.ttl}`
          }
        })
        
        await this.cacheAPI.put(this.getCacheKey(key), response)
      } catch (error) {
        console.warn('[WorkersCache] Failed to store in Cache API:', error)
      }
    }
  }
  
  /**
   * Check if key exists in cache (and is not expired)
   * 
   * @param key - Cache key
   * @returns True if key exists and is not expired
   */
  async has(key: string): Promise<boolean> {
    const result = await this.get(key)
    return result !== null
  }
  
  /**
   * Delete item from cache
   * 
   * @param key - Cache key
   * @returns True if item was deleted
   */
  async delete(key: string): Promise<boolean> {
    // Delete from memory cache
    const memoryDeleted = this.memoryCache.delete(key)
    
    // Delete from Cache API if available
    let cacheDeleted = false
    if (this.cacheAPI) {
      try {
        cacheDeleted = await this.cacheAPI.delete(this.getCacheKey(key))
      } catch (error) {
        console.warn('[WorkersCache] Failed to delete from Cache API:', error)
      }
    }
    
    return memoryDeleted || cacheDeleted
  }
  
  /**
   * Clear all items from cache
   */
  async clear(): Promise<void> {
    // Clear memory cache
    this.memoryCache.clear()
    
    // Note: Cache API doesn't have a clear method
    // We would need to track keys separately to clear them
    console.warn('[WorkersCache] Cache API clear not implemented')
  }
  
  /**
   * Get item from memory cache only
   * 
   * @param key - Cache key
   * @returns Cached data or null
   */
  private getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key)
    
    if (!entry) {
      return null
    }
    
    // Check if entry has expired
    const now = Date.now()
    if (now > entry.timestamp + entry.ttl * 1000) {
      this.memoryCache.delete(key)
      return null
    }
    
    return entry.data as T
  }
  
  /**
   * Generate Cache API key
   *
   * @param key - Original cache key
   * @returns Cache API compatible key (must be a valid URL)
   */
  private getCacheKey(key: string): string {
    // Cache API requires valid URLs as keys
    // Use a dummy domain with the cache key as path
    const encodedKey = encodeURIComponent(key)
    return `https://mdx-cache.local/${encodedKey}`
  }
  
  /**
   * Get cache statistics
   * 
   * @returns Cache statistics
   */
  getStats() {
    const now = Date.now()
    let validEntries = 0
    let expiredEntries = 0
    
    for (const [key, entry] of this.memoryCache.entries()) {
      if (now > entry.timestamp + entry.ttl * 1000) {
        expiredEntries++
      } else {
        validEntries++
      }
    }
    
    return {
      memoryEntries: this.memoryCache.size,
      validEntries,
      expiredEntries,
      hasCacheAPI: !!this.cacheAPI
    }
  }
}
