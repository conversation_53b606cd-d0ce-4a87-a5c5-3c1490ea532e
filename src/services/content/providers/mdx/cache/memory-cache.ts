/**
 * Memory Cache Implementation for MDX Provider
 * 
 * This module provides an in-memory caching solution optimized for
 * Node.js environments like Vercel serverless functions.
 */

import type { CacheEntry } from '../types'

/**
 * In-memory cache implementation
 */
export class MemoryCache {
  private cache = new Map<string, CacheEntry>()
  private defaultTTL: number
  
  constructor(defaultTTL: number = 3600) {
    this.defaultTTL = defaultTTL * 1000 // Convert to milliseconds
  }
  
  /**
   * Get item from cache
   * 
   * @param key - Cache key
   * @returns Cached data or null if not found/expired
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }
    
    // Check if entry has expired
    const now = Date.now()
    if (now > entry.timestamp + entry.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return entry.data as T
  }
  
  /**
   * Set item in cache
   * 
   * @param key - Cache key
   * @param data - Data to cache
   * @param ttl - Time to live in seconds (optional)
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: (ttl || this.defaultTTL / 1000) * 1000 // Convert to milliseconds
    }
    
    this.cache.set(key, entry)
  }
  
  /**
   * Check if key exists in cache (and is not expired)
   * 
   * @param key - Cache key
   * @returns True if key exists and is not expired
   */
  has(key: string): boolean {
    return this.get(key) !== null
  }
  
  /**
   * Delete item from cache
   * 
   * @param key - Cache key
   * @returns True if item was deleted
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }
  
  /**
   * Clear all items from cache
   */
  clear(): void {
    this.cache.clear()
  }
  
  /**
   * Get cache statistics
   * 
   * @returns Cache statistics
   */
  getStats() {
    const now = Date.now()
    let validEntries = 0
    let expiredEntries = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        expiredEntries++
      } else {
        validEntries++
      }
    }
    
    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries,
      hitRate: validEntries / (validEntries + expiredEntries) || 0
    }
  }
  
  /**
   * Clean up expired entries
   * 
   * @returns Number of entries removed
   */
  cleanup(): number {
    const now = Date.now()
    let removed = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key)
        removed++
      }
    }
    
    return removed
  }
  
  /**
   * Get all cache keys
   * 
   * @returns Array of cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }
  
  /**
   * Get cache size in bytes (approximate)
   * 
   * @returns Approximate cache size in bytes
   */
  getSize(): number {
    let size = 0
    
    for (const [key, entry] of this.cache.entries()) {
      // Rough estimation of memory usage
      size += key.length * 2 // UTF-16 characters
      size += JSON.stringify(entry.data).length * 2
      size += 24 // Overhead for timestamp and ttl
    }
    
    return size
  }
}
