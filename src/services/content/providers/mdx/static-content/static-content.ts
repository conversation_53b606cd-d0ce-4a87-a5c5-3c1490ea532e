/**
 * Static Content Index for MDX Provider
 *
 * This file is auto-generated by build-mdx-provider.ts
 * DO NOT EDIT MANUALLY
 *
 * Last generated: 2025-07-25T06:39:04.870Z
 */

import type { MDXContentItem } from '../types'

// Lazy-loaded content chunks
let contentCache: Record<string, MDXContentItem> | null = null

/**
 * Get all static content with lazy loading and caching
 *
 * @returns Complete content bundle
 */
export async function getStaticContent(): Promise<Record<string, MDXContentItem>> {
  // Return cached content if available
  if (contentCache) {
    return contentCache
  }

  console.log('[StaticContent] Loading content chunks...')

  try {
    // Dynamically import all content chunks
    const [blogs, products, casestudies] = await Promise.all([
      import('./static-content-blogs').catch(() => ({ default: {} })),
      import('./static-content-products').catch(() => ({ default: {} })),
      import('./static-content-case-studies').catch(() => ({ default: {} }))
    ])

    // Combine all chunks
    contentCache = {
      ...blogs.default,
      ...products.default,
      ...casestudies.default
    }

    console.log(`[StaticContent] Loaded ${Object.keys(contentCache).length} content items`)
    return contentCache
  } catch (error) {
    console.error('[StaticContent] Failed to load content chunks:', error)
    return {}
  }
}
