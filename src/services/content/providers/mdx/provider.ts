/**
 * MDX Content Provider Implementation
 * 
 * This provider implements the content provider interface using Next.js
 * built-in MDX support with platform-adaptive strategies for optimal
 * performance on both Cloudflare Workers and Vercel deployments.
 */

import type { 
  ContentProvider, 
  ContentItem, 
  ContentType,
  QueryOptions,
  ContentMetadata,
  LanguageVersion 
} from '../../types'
import type { MDXProviderConfig, StrategyInterface } from './types'
import { mergeConfig } from './config'
import { detectStrategy, logPlatformInfo } from './utils/platform-detector'
import { PrecompiledStrategy } from './strategies/precompiled-strategy'
import { FilesystemStrategy } from './strategies/filesystem-strategy'

/**
 * MDX Provider Class
 * 
 * Implements the ContentProvider interface using platform-adaptive strategies.
 * Automatically selects the optimal strategy based on the deployment environment.
 */
export class MDXProvider implements ContentProvider {
  readonly name = 'mdx'
  readonly version = '1.0.0'
  
  private strategy: StrategyInterface
  private config: Required<MDXProviderConfig>
  
  constructor(config?: Partial<MDXProviderConfig>) {
    // Merge user config with defaults
    this.config = mergeConfig(config)
    
    // Create platform-specific strategy
    this.strategy = this.createStrategy()
    
    // Log platform information in development
    if (this.config.developmentMode) {
      logPlatformInfo()
    }
    
    console.log(`[MDXProvider] Initialized with ${this.strategy.name} strategy`)
  }
  
  /**
   * Create the appropriate strategy based on platform detection
   */
  private createStrategy(): StrategyInterface {
    const strategyType = detectStrategy(this.config)
    
    switch (strategyType) {
      case 'precompiled':
        return new PrecompiledStrategy(this.config)
      case 'filesystem':
        return new FilesystemStrategy(this.config)
      default:
        throw new Error(`Unknown strategy type: ${strategyType}`)
    }
  }
  
  /**
   * Get a single content item by type, slug, and locale
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    try {
      return await this.strategy.getContent<T>(type, slug, locale)
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content ${type}/${slug}/${locale}:`, error)
      return null
    }
  }
  
  /**
   * Get a list of content items with optional filtering and sorting
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]> {
    try {
      return await this.strategy.getContentList<T>(type, locale, options)
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content list ${type}/${locale}:`, error)
      return []
    }
  }
  
  /**
   * Get all content for static generation
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    try {
      return await this.strategy.getContentForStaticGeneration<T>(type)
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content for static generation ${type}:`, error)
      return []
    }
  }
  
  /**
   * Get all content slugs for static path generation
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    try {
      return await this.strategy.getAllContentSlugs(type)
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content slugs ${type}:`, error)
      return []
    }
  }
  
  /**
   * Check if content exists
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    try {
      return await this.strategy.contentExists(type, slug, locale)
    } catch (error) {
      console.error(`[MDXProvider] Failed to check content existence ${type}/${slug}/${locale}:`, error)
      return false
    }
  }
  
  /**
   * Get content title only
   */
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null> {
    try {
      return await this.strategy.getContentTitle(type, slug, locale)
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content title ${type}/${slug}/${locale}:`, error)
      return null
    }
  }
  
  /**
   * Get content metadata
   */
  async getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null> {
    try {
      return await this.strategy.getContentMetadata(type, slug, locale)
    } catch (error) {
      console.error(`[MDXProvider] Failed to get content metadata ${type}/${slug}/${locale}:`, error)
      return null
    }
  }
  
  /**
   * Get available language versions for content
   */
  async getLanguageVersions(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]> {
    try {
      const allSlugs = await this.strategy.getAllContentSlugs(type)
      const versions: LanguageVersion[] = []

      for (const item of allSlugs.filter(item => item.slug === slug)) {
        const title = await this.getContentTitle(type, slug, item.locale)
        versions.push({
          lang: item.locale,
          title: title || slug,
          url: `/${item.locale}/${this.getContentTypePath(type)}/${slug}`,
          available: true
        })
      }

      return versions
    } catch (error) {
      console.error(`[MDXProvider] Failed to get language versions ${type}/${slug}:`, error)
      return []
    }
  }
  
  /**
   * Get provider information
   */
  getProviderInfo() {
    return {
      name: this.name,
      version: this.version,
      strategy: this.strategy.name,
      config: {
        contentDir: this.config.contentDir,
        cacheTTL: this.config.cacheTTL,
        developmentMode: this.config.developmentMode
      }
    }
  }
  
  /**
   * Get provider statistics
   */
  getStats() {
    return {
      provider: this.name,
      strategy: this.strategy.name,
      version: this.version,
      // Additional stats could be added here
    }
  }

  /**
   * Get available language versions of content
   */
  async getAvailableLanguages(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]> {
    // Get all supported locales
    const supportedLocales = ['en', 'zh'] // TODO: Make this configurable
    const availableLanguages: LanguageVersion[] = []

    for (const locale of supportedLocales) {
      const exists = await this.contentExists(type, slug, locale)
      if (exists) {
        const title = await this.getContentTitle(type, slug, locale)
        availableLanguages.push({
          lang: locale,
          title: title || slug,
          url: `/${locale}/${this.getContentTypePath(type)}/${slug}`,
          available: true
        })
      }
    }

    return availableLanguages
  }

  /**
   * Get URL path for content type
   */
  private getContentTypePath(type: ContentType): string {
    const pathMap: Record<ContentType, string> = {
      'blog': 'blogs',
      'product': 'products',
      'case-study': 'case-studies'
    }
    return pathMap[type]
  }
}
